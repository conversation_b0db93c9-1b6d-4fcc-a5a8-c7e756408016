# PowerShell script to start Exo with enhanced debugging (context7)
Write-Host "Starting Exo with enhanced debugging (context7)..." -ForegroundColor Green
Write-Host ""

# Set environment variables for debugging
$env:DEBUG = "7"
$env:DEBUG_DISCOVERY = "7" 
$env:TINYGRAD_DEBUG = "2"

Write-Host "Environment variables set:" -ForegroundColor Yellow
Write-Host "  DEBUG = $env:DEBUG"
Write-Host "  DEBUG_DISCOVERY = $env:DEBUG_DISCOVERY"
Write-Host "  TINYGRAD_DEBUG = $env:TINYGRAD_DEBUG"
Write-Host ""

# Start Exo with enhanced configuration
Write-Host "Starting Exo..." -ForegroundColor Green
exo --inference-engine tinygrad `
    --node-id windows-node `
    --node-host 0.0.0.0 `
    --node-port 50051 `
    --listen-port 5678 `
    --broadcast-port 5679 `
    --chatgpt-api-port 52415 `
    --discovery-module udp `
    --discovery-timeout 30 `
    --default-model llama-3.1-8b `
    --max-generate-tokens 2048 `
    --chatgpt-api-response-timeout 900

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
