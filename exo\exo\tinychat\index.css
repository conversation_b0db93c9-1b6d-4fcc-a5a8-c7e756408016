/* define colors */
:root {
  --primary-color: #fff;
  --secondary-color: #2a2a2a;
  --secondary-color-transparent: #ffffff66;
  --primary-bg-color: #1a1a1a;
  --foreground-color: #f0f0f0;
  --red-color: #a52e4d;
}

main {
  width: 100%;
  height: 100%;

  display: flex;
  flex-direction: column;

  place-items: center;
}

.home {
  width: 100%;
  height: 90%;
  margin-bottom: 10rem;
  padding-top: 2rem;
}

.title {
  font-size: 3rem;
  margin: 1rem 0;
  margin-top: 3rem;
}

.histories-container-container {
  width: 100%;
  max-height: 75%;

  position: relative;
}

.histories-container {
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
  height: 100%;

  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;

  margin: 0;
  padding: 3rem 1rem;
}

.histories-start {
  height: 3rem;
  width: 100%;

  z-index: 999;
  top: 0;
  position: absolute;

  background: linear-gradient(
    180deg,
    var(--primary-bg-color) 0%,
    transparent 100%
  );
}
.histories-end {
  height: 3rem;
  width: 100%;

  z-index: 999;
  bottom: 0;
  position: absolute;

  background: linear-gradient(
    0deg,
    var(--primary-bg-color) 0%,
    transparent 100%
  );
}

.history {
  padding: 1rem;
  width: 100%;
  max-width: 40rem;

  background-color: var(--secondary-color);
  border-radius: 10px;
  border-left: 2px solid var(--primary-color);

  cursor: pointer;

  transform: translateX(calc(1px * var(--tx, 0)));
  opacity: var(--opacity, 1);
}
.history:hover {
  background-color: var(--secondary-color);
}

.history-delete-button {
  position: absolute;
  top: 0;
  right: 0;
  padding: 0.5rem;
  margin: 0;
  outline: none;
  border: none;
  background-color: var(--secondary-color);
  color: var(--foreground-color);
  border-radius: 0 0 0 10px;
  cursor: pointer;
  transition: 0.2s;
}
.history-delete-button:hover {
  background-color: var(--secondary-color);
  padding: 0.75rem;
}

.messages {
  overflow-y: auto;
  height: 100%;
  width: 100%;
  max-width: 1200px;

  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
  padding-top: 4rem;
  padding-bottom: 11rem;
  margin: 0 auto;
}

.message {
  max-width: 75%;
  padding: 0.5rem 1rem;
  border-radius: 20px;
}

@media(max-width: 1482px) {
  .messages {
    padding-left: 16px;
    padding-right: 16px;
  }
}

.message-role-assistant {
  background-color: var(--secondary-color);
  margin-right: auto;
  color: #fff;
}
.message-role-user {
  margin-left: auto;
  background-color: var(--primary-color);
  color: #000;
}

.message-role-user p {
  white-space: pre-wrap;
  word-wrap: break-word;
}

.download-progress {
  position: fixed;
  bottom: 11rem;
  left: 50%;
  transform: translateX(-50%);
  margin-left: 125px;
  width: 100%;
  max-width: 1200px;
  overflow-y: auto;
  min-height: 350px;
  padding: 2rem;
  z-index: 998;
}
.message > pre {
  white-space: pre-wrap;
}

.progress-bar-container {
  width: 100%;
  background-color: #e0e0e0;
  border-radius: 4px;
  margin: 10px 0;
}
.progress-bar {
  height: 20px;
  border-radius: 4px;
  transition: width 0.5s ease-in-out;
}
.progress-bar.complete {
  background-color: #4CAF50;
}
.progress-bar.in-progress {
  background-color: #2196F3;
}

.toast {
    width: 100%;
    background-color: #fc2a2a;
    color: #fff;
    text-align: left;
    border-radius: 2px;
    padding: 16px;
    position: fixed;
    z-index: 9999;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    white-space: pre-wrap;
    font-family: monospace;
}

.toast-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.toast-error-message {
    flex-grow: 1;
}

.toast-header-buttons {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-left: 24px;
}

.toast-expand-button {
    background: none;
    border: none;
    color: white;
    padding: 4px;
    cursor: pointer;
    font-size: 1em;
}

.toast-close-button {
    background: none;
    border: none;
    color: white;
    padding: 4px;
    cursor: pointer;
    font-size: 1.2em;
    line-height: 1;
}

.toast-expand-button:hover,
.toast-close-button:hover {
    opacity: 0.8;
}

.toast-content {
    margin-top: 10px;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
}

.hljs {
  width: 100%;
  position: relative;
  border-radius: 10px;
  /* wrap code blocks */
  white-space: pre-wrap;
}
/* put clipboard button in the top right corner of the code block */
.clipboard-button {
  position: absolute;
  top: 0;
  right: 0;
  padding: 0.5rem;
  margin: 0;
  outline: none;
  border: none;
  background-color: var(--secondary-color);
  color: var(--foreground-color);
  border-radius: 0 0 0 10px;
  cursor: pointer;
  transition: 0.2s;
}
.clipboard-button:hover {
  background-color: var(--secondary-color);
  padding: 0.75rem;
}

.input-container {
  position: fixed;
  bottom: 0;
  left: 250px;
  width: calc(100% - 250px);
  max-width: 1200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 999;
  background: linear-gradient(
    0deg,
    var(--primary-bg-color) 55%,
    transparent 100%
  );
  left: 50%;
  transform: translateX(-50%);
  margin-left: 125px;
}

.input-performance {
  margin-top: 4rem;

  display: flex;
  flex-direction: row;
  gap: 1rem;
}

.input-performance-point {
  display: flex;
  flex-direction: row;
  place-items: center;
  gap: 0.5rem;
}
.input-performance-point > p {
  height: 1rem;
  line-height: normal;
}

.input {
  width: 90%;
  min-height: 3rem;
  flex-shrink: 0;

  display: flex;
  flex-direction: row;
  justify-content: center;
  gap: 0.5rem;

  align-items: flex-end;
  margin-bottom: 2rem;
}

.input-form {
  width: 100%;
  padding: 1rem;
  min-height: 3rem;
  max-height: 8rem;

  background-color: var(--secondary-color);
  color: var(--foreground-color);
  border-radius: 10px;
  border: none;
  resize: none;
  outline: none;
}

.input-button {
  height: 3rem;
  width: 4rem;

  background-color: var(--primary-color);
  color: var(--secondary-color);
  border-radius: 10px;
  padding: 0.5rem;
  cursor: pointer;
}
.input-button:hover {
  background-color: var(--secondary-color-transparent);
}
.input-button:disabled {
  background-color: var(--secondary-color);
  cursor: not-allowed;
}

/* wrap text */
p {
  white-space: pre-wrap;
}

/* fonts */
.megrim-regular {
  font-family: "Megrim", system-ui;
  font-weight: 400;
  font-style: normal;
}

.monospace {
  font-family: monospace;
}

.model-selector {
  display: none;
}

/* Image upload button styles */
.image-input-button {
  background-color: var(--secondary-color);
  color: var(--foreground-color);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.image-input-button:hover {
  background-color: var(--secondary-color-transparent);
  transform: scale(1.1);
}

.image-input-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--secondary-color-rgb), 0.5);
}

.image-input-button i {
  transition: all 0.3s ease;
}

.image-input-button:hover i {
  transform: scale(1.2);
}

/* Hidden file input styles */
#image-upload {
  display: none;
}

.image-preview-container {
  position: relative;
  display: inline-block;
  margin-right: 10px;
}

.image-preview {
  max-width: 100px;
  max-height: 100px;
  object-fit: cover;
  border-radius: 5px;
}

.remove-image-button {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: rgba(255, 255, 255, 0.8);
  border: none;
  border-radius: 50%;
  padding: 2px 5px;
  cursor: pointer;
}

.message > p > img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.clear-history-button {
  background-color: var(--red-color);
  color: white;
  padding: 10px 20px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  margin: 1rem auto;
  border: none;
  cursor: pointer;
}

.clear-history-button:hover {
  opacity: 0.8;
  transform: scale(1.05);
}

.clear-history-button i {
  font-size: 14px;
}

/* Add new sidebar styles */
.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  width: 250px;
  background-color: var(--secondary-color);
  padding: 20px;
  overflow-y: auto;
  z-index: 1000;
}

.model-option {
  padding: 12px;
  margin: 8px 0;
  border-radius: 8px;
  background-color: var(--primary-bg-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.model-option:hover {
  transform: translateX(5px);
}

.model-option.selected {
  border-left: 3px solid var(--primary-color);
  background-color: var(--secondary-color-transparent);
}

.model-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.model-progress {
  font-size: 0.9em;
  color: var(--secondary-color-transparent);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.model-progress-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.model-progress i {
  font-size: 0.9em;
  color: var(--primary-color);
}

/* Adjust main content to accommodate sidebar */
main {
  margin-left: 250px;
  width: calc(100% - 250px);
}

/* Add styles for the back button */
.back-button {
  position: fixed;
  top: 1rem;
  left: calc(250px + 1rem); /* Sidebar width + padding */
  background-color: var(--secondary-color);
  color: var(--foreground-color);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  z-index: 1000;
  transition: all 0.2s ease;
}

.back-button:hover {
  transform: translateX(-5px);
  background-color: var(--secondary-color-transparent);
}

.model-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.model-size {
  font-size: 0.8em;
  color: var(--secondary-color-transparent);
  opacity: 0.8;
}

.model-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.model-delete-button {
    background: none;
    border: none;
    color: var(--red-color);
    padding: 4px 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    opacity: 0.7;
}

.model-delete-button:hover {
    opacity: 1;
    transform: scale(1.1);
}

.model-option:hover .model-delete-button {
    opacity: 1;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 20px;
    color: var(--secondary-color-transparent);
}

.loading-container i {
    font-size: 24px;
}

.loading-container span {
    font-size: 14px;
}

/* Add this to your CSS */
.fa-spin {
    animation: fa-spin 2s infinite linear;
}

@keyframes fa-spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.model-download-button {
  background: none;
  border: none;
  color: var(--primary-color);
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background-color: var(--primary-bg-color);
  font-size: 0.9em;
  width: fit-content;
  align-self: flex-start;
}

.model-download-button:hover {
  transform: scale(1.05);
  background-color: var(--secondary-color-transparent);
}

.model-download-button i {
  font-size: 0.9em;
}

.topology-section {
  margin-bottom: 30px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.topology-visualization {
  min-height: 150px;
  position: relative;
  margin-top: 10px;
}

.topology-loading {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #666;
  font-size: 0.9em;
}

.topology-node {
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  margin: 4px 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.node-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9em;
}

.topology-node .status {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  flex-shrink: 0;
}

.topology-node .status.active {
  background: #4CAF50;
}

.topology-node .status.inactive {
  background: #666;
}

.node-details {
  padding-left: 12px;
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 0.8em;
  opacity: 0.6;
}

.node-details span {
  display: flex;
  align-items: center;
}

.peer-connections {
  margin-top: 8px;
  padding-left: 12px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.peer-connection {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.85em;
  color: #a0a0a0;
}

.peer-connection i {
  font-size: 0.8em;
  color: #666;
}

.thinking-block {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  margin: 8px 0;
  overflow: hidden;
}

.thinking-header {
  background-color: rgba(255, 255, 255, 0.1);
  padding: 8px 12px;
  font-size: 0.9em;
  color: #a0a0a0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.thinking-content {
  padding: 12px;
  white-space: pre-wrap;
}

@keyframes thinking-spin {
  to { transform: rotate(360deg); }
}

.thinking-header.thinking::before {
  content: '';
  width: 12px;
  height: 12px;
  border: 2px solid #a0a0a0;
  border-top-color: transparent;
  border-radius: 50%;
  animation: thinking-spin 1s linear infinite;
}

.model-group {
  margin-bottom: 12px;
}

.model-group-header,
.model-subgroup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: var(--primary-bg-color);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 8px;
}

.model-group-header:hover,
.model-subgroup-header:hover {
  background-color: var(--secondary-color-transparent);
}

.model-group-content {
  padding-left: 12px;
}

.model-subgroup {
  margin-bottom: 8px;
}

.model-subgroup-header {
  font-size: 0.9em;
  background-color: rgba(255, 255, 255, 0.05);
}

.model-subgroup-content {
  padding-left: 12px;
}

.group-header-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.model-count {
  font-size: 0.8em;
  color: var(--secondary-color-transparent);
  font-family: monospace;
}
