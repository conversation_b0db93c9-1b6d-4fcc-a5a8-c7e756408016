@echo off
echo Starting Exo with enhanced debugging (context7)...
echo.

REM Set environment variables for debugging
set DEBUG=7
set DEBUG_DISCOVERY=7
set TINYGRAD_DEBUG=2

REM Start Exo with enhanced configuration
exo --inference-engine tinygrad ^
    --node-id windows-node ^
    --node-host 0.0.0.0 ^
    --node-port 50051 ^
    --listen-port 5678 ^
    --broadcast-port 5679 ^
    --chatgpt-api-port 52415 ^
    --discovery-module udp ^
    --discovery-timeout 30 ^
    --default-model llama3.2:3b ^
    --max-generate-tokens 2048 ^
    --chatgpt-api-response-timeout 900

pause
