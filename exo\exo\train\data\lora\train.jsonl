{"text": "table: 1-1000181-1\ncolumns: State/territory, Text/background colour, Format, Current slogan, Current series, Notes\nQ: Tell me what the notes are for South Australia \nA: SELECT Notes FROM 1-1000181-1 WHERE Current slogan = 'SOUTH AUSTRALIA'"}
{"text": "table: 1-1000181-1\ncolumns: State/territory, Text/background colour, Format, Current slogan, Current series, Notes\nQ: What is the current series where the new series began in June 2011?\nA: SELECT Current series FROM 1-1000181-1 WHERE Notes = 'New series began in June 2011'"}
{"text": "table: 1-1000181-1\ncolumns: State/territory, Text/background colour, Format, Current slogan, Current series, Notes\nQ: What is the format for South Australia?\nA: SELECT Format FROM 1-1000181-1 WHERE State/territory = 'South Australia'"}
{"text": "table: 1-1000181-1\ncolumns: State/territory, Text/background colour, Format, Current slogan, Current series, Notes\nQ: Name the background colour for the Australian Capital Territory\nA: SELECT Text/background colour FROM 1-1000181-1 WHERE State/territory = 'Australian Capital Territory'"}
{"text": "table: 1-10007452-3\ncolumns: Order Year, Manufacturer, Model, Fleet Series (Quantity), Powertrain (Engine/Transmission), Fuel Propulsion\nQ: how many times is the fuel propulsion is cng?\nA: SELECT COUNT Fleet Series (Quantity) FROM 1-10007452-3 WHERE Fuel Propulsion = 'CNG'"}
{"text": "table: 1-10007452-3\ncolumns: Order Year, Manufacturer, Model, Fleet Series (Quantity), Powertrain (Engine/Transmission), Fuel Propulsion\nQ: what is the fuel propulsion where the fleet series (quantity) is 310-329 (20)?\nA: SELECT Fuel Propulsion FROM 1-10007452-3 WHERE Fleet Series (Quantity) = '310-329 (20)'"}
{"text": "table: 1-10007452-3\ncolumns: Order Year, Manufacturer, Model, Fleet Series (Quantity), Powertrain (Engine/Transmission), Fuel Propulsion\nQ: who is the manufacturer for the order year 1998?\nA: SELECT Manufacturer FROM 1-10007452-3 WHERE Order Year = '1998'"}
{"text": "table: 1-10007452-3\ncolumns: Order Year, Manufacturer, Model, Fleet Series (Quantity), Powertrain (Engine/Transmission), Fuel Propulsion\nQ: how many times is the model ge40lfr?\nA: SELECT COUNT Manufacturer FROM 1-10007452-3 WHERE Model = 'GE40LFR'"}
{"text": "table: 1-10007452-3\ncolumns: Order Year, Manufacturer, Model, Fleet Series (Quantity), Powertrain (Engine/Transmission), Fuel Propulsion\nQ: how many times is the fleet series (quantity) is 468-473 (6)?\nA: SELECT COUNT Order Year FROM 1-10007452-3 WHERE Fleet Series (Quantity) = '468-473 (6)'"}
{"text": "table: 1-10007452-3\ncolumns: Order Year, Manufacturer, Model, Fleet Series (Quantity), Powertrain (Engine/Transmission), Fuel Propulsion\nQ: what is the powertrain (engine/transmission) when the order year is 2000?\nA: SELECT Powertrain (Engine/Transmission) FROM 1-10007452-3 WHERE Order Year = '2000'"}
{"text": "table: 1-10006830-1\ncolumns: Aircraft, Description, Max Gross Weight, Total disk area, Max disk Loading\nQ: What if the description of a ch-47d chinook?\nA: SELECT Description FROM 1-10006830-1 WHERE Aircraft = 'CH-47D Chinook'"}
{"text": "table: 1-10006830-1\ncolumns: Aircraft, Description, Max Gross Weight, Total disk area, Max disk Loading\nQ: What is the max gross weight of the Robinson R-22?\nA: SELECT Max Gross Weight FROM 1-10006830-1 WHERE Aircraft = 'Robinson R-22'"}
{"text": "table: 1-********-1\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: What school did player number 6 come from?\nA: SELECT School/Club Team FROM 1-********-1 WHERE No. = '6'"}
{"text": "table: 1-********-1\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: What school did the player that has been in Toronto from 2012-present come from?\nA: SELECT School/Club Team FROM 1-********-1 WHERE Years in Toronto = '2012-present'"}
{"text": "table: 1-********-1\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: What school did the player that has been in Toronto from 2010-2012 go to?\nA: SELECT School/Club Team FROM 1-********-1 WHERE Years in Toronto = '2010-2012'"}
{"text": "table: 1-********-1\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: What position did the player from Baylor play?\nA: SELECT Position FROM 1-********-1 WHERE School/Club Team = 'Baylor'"}
{"text": "table: 1-********-14\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: Who played in the Toronto Raptors from 1995-96?\nA: SELECT Player FROM 1-********-14 WHERE Years in Toronto = '1995-96'"}
{"text": "table: 1-********-14\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: Which number was Patrick O'Bryant?\nA: SELECT No. FROM 1-********-14 WHERE Player = 'Patrick O'Bryant'"}
{"text": "table: 1-********-14\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: What school did Patrick O'Bryant play for?\nA: SELECT School/Club Team FROM 1-********-14 WHERE Player = 'Patrick O'Bryant'"}
{"text": "table: 1-********-14\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: How many number does Fordham school have?\nA: SELECT COUNT No. FROM 1-********-14 WHERE School/Club Team = 'Fordham'"}
{"text": "table: 1-********-14\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: Which school was in Toronto in 2001-02?\nA: SELECT School/Club Team FROM 1-********-14 WHERE Years in Toronto = '2001-02'"}
{"text": "table: 1-********-21\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: Which school did the player that played 2004-05 attend?\nA: SELECT School/Club Team FROM 1-********-21 WHERE Years in Toronto = '2004-05'"}
{"text": "table: 1-********-21\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: Which position does Loren Woods play?\nA: SELECT Position FROM 1-********-21 WHERE Player = 'Loren Woods'"}
{"text": "table: 1-********-21\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: What number is the player that played 1998-2001\nA: SELECT MIN No. FROM 1-********-21 WHERE Years in Toronto = '1998-2001'"}
{"text": "table: 1-********-21\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: Which country is the player that went to Georgetown from?\nA: SELECT Nationality FROM 1-********-21 WHERE School/Club Team = 'Georgetown'"}
{"text": "table: 1-********-21\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: Which school did Herb Williams go to?\nA: SELECT School/Club Team FROM 1-********-21 WHERE Player = 'Herb Williams'"}
{"text": "table: 1-********-3\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: When did the player from Hawaii play for Toronto?\nA: SELECT Years in Toronto FROM 1-********-3 WHERE School/Club Team = 'Hawaii'"}
{"text": "table: 1-********-3\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: During what period did Dell Curry play for Toronto?\nA: SELECT Years in Toronto FROM 1-********-3 WHERE Player = 'Dell Curry'"}
{"text": "table: 1-********-3\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: What's the number of the player from Boise State?\nA: SELECT No. FROM 1-********-3 WHERE School/Club Team = 'Boise State'"}
{"text": "table: 1-********-3\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: What's Dell Curry nationality?\nA: SELECT Nationality FROM 1-********-3 WHERE Player = 'Dell Curry'"}
{"text": "table: 1-********-7\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: which player is from georgia\nA: SELECT Player FROM 1-********-7 WHERE School/Club Team = 'Georgia'"}
{"text": "table: 1-********-7\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: what school is rudy gay from\nA: SELECT School/Club Team FROM 1-********-7 WHERE Player = 'Rudy Gay'"}
{"text": "table: 1-********-7\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: what nationality is the player who played from 1997-98\nA: SELECT Nationality FROM 1-********-7 WHERE Years in Toronto = '1997-98'"}
{"text": "table: 1-********-7\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: what position did the player from connecticut play\nA: SELECT Position FROM 1-********-7 WHERE School/Club Team = 'Connecticut'"}
{"text": "table: 1-********-2\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: During which years was Marcus Banks in Toronto?\nA: SELECT Years in Toronto FROM 1-********-2 WHERE Player = 'Marcus Banks'"}
{"text": "table: 1-********-2\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: Which positions were in Toronto in 2004?\nA: SELECT Position FROM 1-********-2 WHERE Years in Toronto = '2004'"}
{"text": "table: 1-********-2\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: What nationality is the player Muggsy Bogues?\nA: SELECT Nationality FROM 1-********-2 WHERE Player = 'Muggsy Bogues'"}
{"text": "table: 1-********-2\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: What years was the player Lonny Baxter in Toronto?\nA: SELECT Years in Toronto FROM 1-********-2 WHERE Player = 'Lonny Baxter'"}
{"text": "table: 1-********-2\ncolumns: Player, No., Nationality, Position, Years in Toronto, School/Club Team\nQ: How many players were with the school or club team La Salle?\nA: SELECT COUNT Player FROM 1-********-2 WHERE School/Club Team = 'La Salle'"}
{"text": "table: 1-********-3\ncolumns: Year, Tournaments played, Cuts made*, Wins, 2nd, Top 10s, Best finish, Earnings ($), Money list rank, Scoring average, Scoring rank\nQ: When the scoring rank was 117, what was the best finish?\nA: SELECT Best finish FROM 1-********-3 WHERE Scoring rank = '117'"}
{"text": "table: 1-********-3\ncolumns: Year, Tournaments played, Cuts made*, Wins, 2nd, Top 10s, Best finish, Earnings ($), Money list rank, Scoring average, Scoring rank\nQ: When the best finish was T69, how many people came in 2nd?\nA: SELECT 2nd FROM 1-********-3 WHERE Best finish = 'T69'"}
{"text": "table: 1-********-3\ncolumns: Year, Tournaments played, Cuts made*, Wins, 2nd, Top 10s, Best finish, Earnings ($), Money list rank, Scoring average, Scoring rank\nQ: How many wins were there when the money list rank was 183?\nA: SELECT COUNT Wins FROM 1-********-3 WHERE Money list rank = '183'"}
{"text": "table: 1-********-3\ncolumns: Year, Tournaments played, Cuts made*, Wins, 2nd, Top 10s, Best finish, Earnings ($), Money list rank, Scoring average, Scoring rank\nQ: When the money list rank was n/a, what was the scoring average?\nA: SELECT Scoring average FROM 1-********-3 WHERE Money list rank = 'n/a'"}
{"text": "table: 1-********-3\ncolumns: Year, Tournaments played, Cuts made*, Wins, 2nd, Top 10s, Best finish, Earnings ($), Money list rank, Scoring average, Scoring rank\nQ: What time was the highest for 2nd finishers?\nA: SELECT MAX 2nd FROM 1-********-3"}
{"text": "table: 1-1004033-1\ncolumns: Season, Player, Position, Nationality, Team, Draft Pick #, Draft Class, College\nQ: When did the Metrostars have their first Rookie of the Year winner?\nA: SELECT MIN Season FROM 1-1004033-1 WHERE Team = 'MetroStars'"}
{"text": "table: 1-1004033-1\ncolumns: Season, Player, Position, Nationality, Team, Draft Pick #, Draft Class, College\nQ: What college did the Rookie of the Year from the Columbus Crew attend?\nA: SELECT College FROM 1-1004033-1 WHERE Team = 'Columbus Crew'"}
{"text": "table: 1-1004033-1\ncolumns: Season, Player, Position, Nationality, Team, Draft Pick #, Draft Class, College\nQ: How many teams had a #1 draft pick that won the Rookie of the Year Award?\nA: SELECT COUNT Team FROM 1-1004033-1 WHERE Draft Pick # = '1'"}
{"text": "table: 1-1004033-1\ncolumns: Season, Player, Position, Nationality, Team, Draft Pick #, Draft Class, College\nQ: What position did the #10 draft pick play?\nA: SELECT Position FROM 1-1004033-1 WHERE Draft Pick # = '10'"}
{"text": "table: 1-10023387-1\ncolumns: Player, Years Played, Total W-L, Singles W-L, Doubles W-L\nQ: what's the\u00a0years played\u00a0with\u00a0singles w-l\u00a0of 3\u20132\nA: SELECT Years Played FROM 1-10023387-1 WHERE Singles W-L = '3\u20132'"}
{"text": "table: 1-10023387-1\ncolumns: Player, Years Played, Total W-L, Singles W-L, Doubles W-L\nQ: what's the\u00a0doubles w-l\u00a0for player\u00a0seol jae-min (none)\nA: SELECT Doubles W-L FROM 1-10023387-1 WHERE Player = 'Seol Jae-Min (none)'"}
{"text": "table: 1-10023387-1\ncolumns: Player, Years Played, Total W-L, Singles W-L, Doubles W-L\nQ: what's the\u00a0singles w-l\u00a0for kim doo-hwan\nA: SELECT Singles W-L FROM 1-10023387-1 WHERE Player = 'Kim Doo-Hwan'"}
{"text": "table: 1-10023387-1\ncolumns: Player, Years Played, Total W-L, Singles W-L, Doubles W-L\nQ: what's the total number of\u00a0singles w-l\u00a0with\u00a0doubles w-l\u00a0of 0\u20130 and\u00a0total w-l\u00a0of 3\u20131\nA: SELECT COUNT Singles W-L FROM 1-10023387-1 WHERE Doubles W-L = '0\u20130' AND Total W-L = '3\u20131'"}
{"text": "table: 1-10023387-1\ncolumns: Player, Years Played, Total W-L, Singles W-L, Doubles W-L\nQ: what's the\u00a0doubles w-l\u00a0with\u00a0years played\u00a0value of 1 (1968)\nA: SELECT Doubles W-L FROM 1-10023387-1 WHERE Years Played = '1 (1968)'"}
{"text": "table: 1-10023387-1\ncolumns: Player, Years Played, Total W-L, Singles W-L, Doubles W-L\nQ: what\u00a0years are played\u00a0for player\u00a0 im chung-yang\nA: SELECT Years Played FROM 1-10023387-1 WHERE Player = 'Im Chung-Yang'"}
{"text": "table: 1-10020178-1\ncolumns: Name, Canton, Height (meters), Crest length (meters), Type, Year of construction, Name of the Lake\nQ: What is the name of the 375 crest length?\nA: SELECT Name FROM 1-10020178-1 WHERE Crest length (meters) = 375"}
{"text": "table: 1-10020178-1\ncolumns: Name, Canton, Height (meters), Crest length (meters), Type, Year of construction, Name of the Lake\nQ: What is year of construction of spitallamm?\nA: SELECT MIN Year of construction FROM 1-10020178-1 WHERE Name = 'Spitallamm'"}
{"text": "table: 1-10020178-1\ncolumns: Name, Canton, Height (meters), Crest length (meters), Type, Year of construction, Name of the Lake\nQ: What is the canton of grande dixence?\nA: SELECT Canton FROM 1-10020178-1 WHERE Name = 'Grande Dixence'"}
{"text": "table: 1-10020178-1\ncolumns: Name, Canton, Height (meters), Crest length (meters), Type, Year of construction, Name of the Lake\nQ: What is the name where lago di luzzone is?\nA: SELECT Name FROM 1-10020178-1 WHERE Name of the Lake = 'Lago di Luzzone'"}
{"text": "table: 1-100518-1\ncolumns: Name, Direction, Mantra, Weapon, Consort, Graha (Planet), Guardian M\u0101t\u1e5bk\u0101\nQ: What is the  guardian m\u0101t\u1e5bk\u0101 for the guardian whose consort is Sv\u0101h\u0101?\nA: SELECT Guardian M\u0101t\u1e5bk\u0101 FROM 1-100518-1 WHERE Consort = 'Sv\u0101h\u0101'"}
{"text": "table: 1-100518-1\ncolumns: Name, Direction, Mantra, Weapon, Consort, Graha (Planet), Guardian M\u0101t\u1e5bk\u0101\nQ: Where the mantra is \"o\u1e43 ya\u1e43 v\u0101yuve nama\u1e25\", what is the direction of the guardian?\nA: SELECT Direction FROM 1-100518-1 WHERE Mantra = 'O\u1e43 Ya\u1e43 V\u0101yuve Nama\u1e25'"}
{"text": "table: 1-100518-1\ncolumns: Name, Direction, Mantra, Weapon, Consort, Graha (Planet), Guardian M\u0101t\u1e5bk\u0101\nQ: What weapon is used by the guardian whose consort is \u015bac\u012b?\nA: SELECT Weapon FROM 1-100518-1 WHERE Consort = '\u015aac\u012b'"}
{"text": "table: 1-100518-1\ncolumns: Name, Direction, Mantra, Weapon, Consort, Graha (Planet), Guardian M\u0101t\u1e5bk\u0101\nQ: What are the directions for the guardian whose weapon is kha\u1e0dga (sword)?\nA: SELECT Direction FROM 1-100518-1 WHERE Weapon = 'Kha\u1e0dga (sword)'"}
{"text": "table: 1-100518-1\ncolumns: Name, Direction, Mantra, Weapon, Consort, Graha (Planet), Guardian M\u0101t\u1e5bk\u0101\nQ: What are the weapons used by guardians for the direction East?\nA: SELECT Weapon FROM 1-100518-1 WHERE Direction = 'East'"}
{"text": "table: 1-100518-1\ncolumns: Name, Direction, Mantra, Weapon, Consort, Graha (Planet), Guardian M\u0101t\u1e5bk\u0101\nQ: What are the directions for the guardian whose graha (planet) is b\u1e5bhaspati (Jupiter)?\nA: SELECT Direction FROM 1-100518-1 WHERE Graha (Planet) = 'B\u1e5bhaspati (Jupiter)'"}
{"text": "table: 1-10054296-1\ncolumns: Member, Headquarters, Classification, Chapters, Founded, UCCFS\nQ: What is the number of chapters listed for the fraternity with a headquarters in Austin, Texas?\nA: SELECT MAX Chapters FROM 1-10054296-1 WHERE Classification = 'Fraternity' AND Headquarters = 'Austin, Texas'"}
{"text": "table: 1-10054296-1\ncolumns: Member, Headquarters, Classification, Chapters, Founded, UCCFS\nQ: What are the members listed with the sorority classification\nA: SELECT Member FROM 1-10054296-1 WHERE Classification = 'Sorority'"}
{"text": "table: 1-10054296-1\ncolumns: Member, Headquarters, Classification, Chapters, Founded, UCCFS\nQ: Name the member that has 12 chapters\nA: SELECT Member FROM 1-10054296-1 WHERE Chapters = 12"}
{"text": "table: 1-10054296-1\ncolumns: Member, Headquarters, Classification, Chapters, Founded, UCCFS\nQ: Where is the headquarters of Alpha Nu Omega\nA: SELECT Headquarters FROM 1-10054296-1 WHERE Member = 'Alpha Nu Omega'"}
{"text": "table: 1-1007688-1\ncolumns: Year, Typhus, Typhoid fever, Relapsing fever, Smallpox, Malaria\nQ: what is the number of relapsing fever when malaria is 3000\nA: SELECT MIN Relapsing fever FROM 1-1007688-1 WHERE Malaria = '3000'"}
{"text": "table: 1-1007688-1\ncolumns: Year, Typhus, Typhoid fever, Relapsing fever, Smallpox, Malaria\nQ: what is the typhoid fever number for the year 1934\nA: SELECT Typhoid fever FROM 1-1007688-1 WHERE Year = '1934'"}
{"text": "table: 1-1007688-1\ncolumns: Year, Typhus, Typhoid fever, Relapsing fever, Smallpox, Malaria\nQ: What are all the typhus number when smallpox is 4\nA: SELECT Typhus FROM 1-1007688-1 WHERE Smallpox = 4"}
{"text": "table: 1-1007688-1\ncolumns: Year, Typhus, Typhoid fever, Relapsing fever, Smallpox, Malaria\nQ: what is the number of smallpox when typhoid fever is 293\nA: SELECT MAX Smallpox FROM 1-1007688-1 WHERE Typhoid fever = 293"}
{"text": "table: 1-1007688-1\ncolumns: Year, Typhus, Typhoid fever, Relapsing fever, Smallpox, Malaria\nQ: what is the typhoid fever number for the year 1929\nA: SELECT Typhoid fever FROM 1-1007688-1 WHERE Year = '1929'"}
{"text": "table: 1-10082596-1\ncolumns: School, Location, Founded, Affiliation, Enrollment, Team Nickname, Primary conference\nQ: How many schools are in Bloomington, IN?\nA: SELECT COUNT Founded FROM 1-10082596-1 WHERE Location = 'Bloomington, IN'"}
{"text": "table: 1-10082596-1\ncolumns: School, Location, Founded, Affiliation, Enrollment, Team Nickname, Primary conference\nQ: How many of the schools are designated private/Presbyterian?\nA: SELECT COUNT Location FROM 1-10082596-1 WHERE Affiliation = 'Private/Presbyterian'"}
{"text": "table: 1-10082596-1\ncolumns: School, Location, Founded, Affiliation, Enrollment, Team Nickname, Primary conference\nQ: In what year was Lindenwood University founded?\nA: SELECT MIN Founded FROM 1-10082596-1 WHERE School = 'Lindenwood University'"}
{"text": "table: 1-10082596-1\ncolumns: School, Location, Founded, Affiliation, Enrollment, Team Nickname, Primary conference\nQ: How many of the schools listed are in Ames, IA?\nA: SELECT COUNT Primary conference FROM 1-10082596-1 WHERE Location = 'Ames, IA'"}
{"text": "table: 1-1008653-9\ncolumns: Country ( exonym ), Capital ( exonym ), Country ( endonym ), Capital ( endonym ), Official or native language(s) (alphabet/script)\nQ: What is the capital (endonym) where Douglas is the Capital (exonym)?\nA: SELECT Capital ( endonym ) FROM 1-1008653-9 WHERE Capital ( exonym ) = 'Douglas'"}
{"text": "table: 1-1008653-9\ncolumns: Country ( exonym ), Capital ( exonym ), Country ( endonym ), Capital ( endonym ), Official or native language(s) (alphabet/script)\nQ: How many countries (endonym) has the capital (endonym) of Jakarta?\nA: SELECT COUNT Country ( endonym ) FROM 1-1008653-9 WHERE Capital ( endonym ) = 'Jakarta'"}
{"text": "table: 1-1008653-9\ncolumns: Country ( exonym ), Capital ( exonym ), Country ( endonym ), Capital ( endonym ), Official or native language(s) (alphabet/script)\nQ: What is the country (exonym) where the official or native language(s) (alphabet/script) is Icelandic?\nA: SELECT Country ( exonym ) FROM 1-1008653-9 WHERE Official or native language(s) (alphabet/script) = 'Icelandic'"}
{"text": "table: 1-1008653-9\ncolumns: Country ( exonym ), Capital ( exonym ), Country ( endonym ), Capital ( endonym ), Official or native language(s) (alphabet/script)\nQ: In which country (endonym) is Irish English the official or native language(s) (alphabet/script)?\nA: SELECT Country ( endonym ) FROM 1-1008653-9 WHERE Official or native language(s) (alphabet/script) = 'Irish English'"}
{"text": "table: 1-1008653-9\ncolumns: Country ( exonym ), Capital ( exonym ), Country ( endonym ), Capital ( endonym ), Official or native language(s) (alphabet/script)\nQ: Which country (exonym) is the country (endonym) isle of man ellan vannin?\nA: SELECT Country ( exonym ) FROM 1-1008653-9 WHERE Country ( endonym ) = 'Isle of Man Ellan Vannin'"}
{"text": "table: 1-1009087-1\ncolumns: Season, Network, Season premiere, Season finale, TV season, Ranking, Viewers (in millions)\nQ: The season premiere aired on September 11, 2000 aired on how many networks? \nA: SELECT COUNT Network FROM 1-1009087-1 WHERE Season premiere = 'September 11, 2000'"}
{"text": "table: 1-1009087-1\ncolumns: Season, Network, Season premiere, Season finale, TV season, Ranking, Viewers (in millions)\nQ: What was the ranking of the season finale aired on May 8, 2006? \nA: SELECT Ranking FROM 1-1009087-1 WHERE Season finale = 'May 8, 2006'"}
{"text": "table: 1-1011906-1\ncolumns: Regional County Municipality (RCM), Population Canada 2011 Census, Land Area, Density (pop. per km2), Seat of RCM\nQ: what is the minimum\u00a0population canada 2011 census\u00a0with\u00a0seat of rcm\u00a0being cowansville\nA: SELECT MIN Population Canada 2011 Census FROM 1-1011906-1 WHERE Seat of RCM = 'Cowansville'"}
{"text": "table: 1-1011906-1\ncolumns: Regional County Municipality (RCM), Population Canada 2011 Census, Land Area, Density (pop. per km2), Seat of RCM\nQ: what's the\u00a0land area\u00a0with\u00a0seat of rcm\u00a0being granby\nA: SELECT Land Area FROM 1-1011906-1 WHERE Seat of RCM = 'Granby'"}
{"text": "table: 1-101196-1\ncolumns: County, English name, Irish name, Population, Irish speakers\nQ: What is the population for County Mayo with the English Name Carrowteige?\nA: SELECT Population FROM 1-101196-1 WHERE County = 'County Mayo' AND English name = 'Carrowteige'"}
{"text": "table: 1-101196-1\ncolumns: County, English name, Irish name, Population, Irish speakers\nQ: What is the Irish name listed with 62% Irish speakers?\nA: SELECT Irish name FROM 1-101196-1 WHERE Irish speakers = '62%'"}
{"text": "table: 1-101196-1\ncolumns: County, English name, Irish name, Population, Irish speakers\nQ: What is the population for the Irish Name Leitir meall\u00e1in?\nA: SELECT Population FROM 1-101196-1 WHERE Irish name = 'Leitir Meall\u00e1in'"}
{"text": "table: 1-101196-1\ncolumns: County, English name, Irish name, Population, Irish speakers\nQ: What is the county for the Irish name Carna?\nA: SELECT County FROM 1-101196-1 WHERE Irish name = 'Carna'"}
{"text": "table: 1-101196-1\ncolumns: County, English name, Irish name, Population, Irish speakers\nQ: How many County Kerry have 53% Irish speakers?\nA: SELECT COUNT English name FROM 1-101196-1 WHERE Irish speakers = '53%' AND County = 'County Kerry'"}
{"text": "table: 1-101196-1\ncolumns: County, English name, Irish name, Population, Irish speakers\nQ: What is the population for the English name Spiddal?\nA: SELECT Population FROM 1-101196-1 WHERE English name = 'Spiddal'"}
{"text": "table: 1-10118412-6\ncolumns: State/Territory, Asian American Population (2010 Census), Chinese, Filipino, Indian, Japanese, Korean, Vietnamese, Other Asian\nQ: What is the the Chinese population for the state that has a Filipino population of 1474707?\nA: SELECT MIN Chinese FROM 1-10118412-6 WHERE Filipino = 1474707"}
{"text": "table: 1-10118412-6\ncolumns: State/Territory, Asian American Population (2010 Census), Chinese, Filipino, Indian, Japanese, Korean, Vietnamese, Other Asian\nQ: How many States have an Indian population of 30947?\nA: SELECT COUNT Filipino FROM 1-10118412-6 WHERE Indian = 30947"}
{"text": "table: 1-10118412-6\ncolumns: State/Territory, Asian American Population (2010 Census), Chinese, Filipino, Indian, Japanese, Korean, Vietnamese, Other Asian\nQ: What is the highest Indian population?\nA: SELECT MAX Indian FROM 1-10118412-6"}
{"text": "table: 1-10121127-1\ncolumns: UN Operation name, UN Operation title, Location, Dates of Australian involvement, Number of Australians involved, Australian role\nQ: What is Australia's role in the UN operation Unama?\nA: SELECT Australian role FROM 1-10121127-1 WHERE UN Operation name = 'UNAMA'"}
{"text": "table: 1-10121127-1\ncolumns: UN Operation name, UN Operation title, Location, Dates of Australian involvement, Number of Australians involved, Australian role\nQ: What is the UN operation title with the UN operation name, Uncok?\nA: SELECT UN Operation title FROM 1-10121127-1 WHERE UN Operation name = 'UNCOK'"}
{"text": "table: 1-10121127-1\ncolumns: UN Operation name, UN Operation title, Location, Dates of Australian involvement, Number of Australians involved, Australian role\nQ: How many Australians were in the UN commission on Korea?\nA: SELECT COUNT Number of Australians involved FROM 1-10121127-1 WHERE UN Operation title = 'UN Commission on Korea'"}
{"text": "table: 1-10121127-1\ncolumns: UN Operation name, UN Operation title, Location, Dates of Australian involvement, Number of Australians involved, Australian role\nQ: When was it where 65 Australians were involved in the UN?\nA: SELECT Dates of Australian involvement FROM 1-10121127-1 WHERE Number of Australians involved = '65'"}
{"text": "table: 1-10120207-8\ncolumns: Season, Timeslot ( ET ), Season premiere, Season finale, TV season, Rank, Viewers (millions)\nQ: What year is the season with the 10.73 million views?\nA: SELECT TV season FROM 1-10120207-8 WHERE Viewers (millions) = '10.73'"}
{"text": "table: 1-10120207-8\ncolumns: Season, Timeslot ( ET ), Season premiere, Season finale, TV season, Rank, Viewers (millions)\nQ: What is the season year where the rank is 39?\nA: SELECT TV season FROM 1-10120207-8 WHERE Rank = '39'"}
{"text": "table: 1-10120207-8\ncolumns: Season, Timeslot ( ET ), Season premiere, Season finale, TV season, Rank, Viewers (millions)\nQ: What is the number of season premieres were 10.17 people watched?\nA: SELECT COUNT Season premiere FROM 1-10120207-8 WHERE Viewers (millions) = '10.17'"}
{"text": "table: 1-10120207-8\ncolumns: Season, Timeslot ( ET ), Season premiere, Season finale, TV season, Rank, Viewers (millions)\nQ: What is the year of the season that was 12?\nA: SELECT TV season FROM 1-10120207-8 WHERE Season = 12"}
{"text": "table: 1-1012730-1\ncolumns: Year, Starts, Wins, Top 5, Top 10, Poles, Avg. Start, Avg. Finish, Winnings, Position, Team(s)\nQ: In 2012 what was the average finish?\nA: SELECT Avg. Finish FROM 1-1012730-1 WHERE Year = 2012"}
{"text": "table: 1-1012730-1\ncolumns: Year, Starts, Wins, Top 5, Top 10, Poles, Avg. Start, Avg. Finish, Winnings, Position, Team(s)\nQ: How many wins happened in 1983?\nA: SELECT MIN Wins FROM 1-1012730-1 WHERE Year = 1983"}
{"text": "table: 1-1012730-1\ncolumns: Year, Starts, Wins, Top 5, Top 10, Poles, Avg. Start, Avg. Finish, Winnings, Position, Team(s)\nQ: How many top tens had an average start of 29.4?\nA: SELECT COUNT Top 10 FROM 1-1012730-1 WHERE Avg. Start = '29.4'"}
{"text": "table: 1-1012730-1\ncolumns: Year, Starts, Wins, Top 5, Top 10, Poles, Avg. Start, Avg. Finish, Winnings, Position, Team(s)\nQ: How many poles had an average finish of 19.1?\nA: SELECT MAX Poles FROM 1-1012730-1 WHERE Avg. Finish = '19.1'"}
{"text": "table: 1-1012730-1\ncolumns: Year, Starts, Wins, Top 5, Top 10, Poles, Avg. Start, Avg. Finish, Winnings, Position, Team(s)\nQ: How many starts did Hendrick motorsports have?\nA: SELECT MIN Starts FROM 1-1012730-1 WHERE Team(s) = 'Hendrick Motorsports'"}
{"text": "table: 1-1013129-10\ncolumns: Pick, Player, Position, Nationality, NHL team, College/junior/club team\nQ: NHL players are all centre in Florida panthers.\nA: SELECT Player FROM 1-1013129-10 WHERE Position = 'Centre' AND NHL team = 'Florida Panthers'"}
{"text": "table: 1-1013129-10\ncolumns: Pick, Player, Position, Nationality, NHL team, College/junior/club team\nQ: NHL team player San Jose Sharks is United States nationally.\nA: SELECT Player FROM 1-1013129-10 WHERE NHL team = 'San Jose Sharks' AND Nationality = 'United States'"}
{"text": "table: 1-1013129-10\ncolumns: Pick, Player, Position, Nationality, NHL team, College/junior/club team\nQ: All players are position mark polak.\nA: SELECT Position FROM 1-1013129-10 WHERE Player = 'Mark Polak'"}
{"text": "table: 1-1013129-10\ncolumns: Pick, Player, Position, Nationality, NHL team, College/junior/club team\nQ: Position in nhl team centre are all smaller pick than 243.0\nA: SELECT NHL team FROM 1-1013129-10 WHERE Position = 'Centre' AND Pick < 243.0"}
{"text": "table: 1-1013129-11\ncolumns: Pick, Player, Position, Nationality, NHL team, College/junior/club team\nQ: What college/junior/club teams do the players from the St. Louis Blues come from?\nA: SELECT College/junior/club team FROM 1-1013129-11 WHERE NHL team = 'St. Louis Blues'"}
{"text": "table: 1-1013129-11\ncolumns: Pick, Player, Position, Nationality, NHL team, College/junior/club team\nQ: What teams do the players from TPS (Finland) play for?\nA: SELECT NHL team FROM 1-1013129-11 WHERE College/junior/club team = 'TPS (Finland)'"}
{"text": "table: 1-1013129-11\ncolumns: Pick, Player, Position, Nationality, NHL team, College/junior/club team\nQ: What high school team did Doug Nolan play for?\nA: SELECT College/junior/club team FROM 1-1013129-11 WHERE Player = 'Doug Nolan'"}
{"text": "table: 1-1013129-11\ncolumns: Pick, Player, Position, Nationality, NHL team, College/junior/club team\nQ: What club team is Per Gustafsson play for?\nA: SELECT College/junior/club team FROM 1-1013129-11 WHERE Player = 'Per Gustafsson'"}
{"text": "table: 1-1013129-11\ncolumns: Pick, Player, Position, Nationality, NHL team, College/junior/club team\nQ: What is the nationality of Shayne Wright?\nA: SELECT Nationality FROM 1-1013129-11 WHERE Player = 'Shayne Wright'"}
{"text": "table: 1-10128185-2\ncolumns: Song, Mobiles, Northern Ireland, Northern England, Scotland, Southern England, Wales, Total\nQ: How many votes did Southern England cast whilst Northern Ireland cast 3?\nA: SELECT Southern England FROM 1-10128185-2 WHERE Northern Ireland = 3"}
{"text": "table: 1-10128185-2\ncolumns: Song, Mobiles, Northern Ireland, Northern England, Scotland, Southern England, Wales, Total\nQ: What was the lowest number of votes Scotland cast?\nA: SELECT MIN Scotland FROM 1-10128185-2"}
{"text": "table: 1-10128185-2\ncolumns: Song, Mobiles, Northern Ireland, Northern England, Scotland, Southern England, Wales, Total\nQ: What is the total number of votes if Scotland cast 35?\nA: SELECT COUNT Scotland FROM 1-10128185-2 WHERE Total = 35"}
{"text": "table: 1-10128185-2\ncolumns: Song, Mobiles, Northern Ireland, Northern England, Scotland, Southern England, Wales, Total\nQ: How many votes did Northern Ireland cast if the total was 35?\nA: SELECT Northern Ireland FROM 1-10128185-2 WHERE Total = 35"}
{"text": "table: 1-10128185-2\ncolumns: Song, Mobiles, Northern Ireland, Northern England, Scotland, Southern England, Wales, Total\nQ: How many votes did Wales cast when Northern England cast 6?\nA: SELECT MIN Wales FROM 1-10128185-2 WHERE Northern England = 6"}
{"text": "table: 1-1012730-2\ncolumns: Year, Starts, Wins, Top 5, Top 10, Poles, Avg. Start, Avg. Finish, Winnings, Position, Team(s)\nQ: What teams had 9 in the top 5 and 1 wins?\nA: SELECT Team(s) FROM 1-1012730-2 WHERE Top 5 = 9 AND Wins = 1"}
{"text": "table: 1-1013129-1\ncolumns: Pick, Player, Position, Nationality, NHL team, College/junior/club team\nQ: What teams did the player vadim sharifijanov play for?\nA: SELECT College/junior/club team FROM 1-1013129-1 WHERE Player = 'Vadim Sharifijanov'"}
{"text": "table: 1-1013129-1\ncolumns: Pick, Player, Position, Nationality, NHL team, College/junior/club team\nQ: What positions do the hartford whalers nhl team have?\nA: SELECT Position FROM 1-1013129-1 WHERE NHL team = 'Hartford Whalers'"}
{"text": "table: 1-1013129-1\ncolumns: Pick, Player, Position, Nationality, NHL team, College/junior/club team\nQ: What is the smallest pick for the player, brett lindros?\nA: SELECT MIN Pick FROM 1-1013129-1 WHERE Player = 'Brett Lindros'"}
{"text": "table: 1-1013129-1\ncolumns: Pick, Player, Position, Nationality, NHL team, College/junior/club team\nQ: What positions does the college/junior/club team, molot perm (russia) have?\nA: SELECT Position FROM 1-1013129-1 WHERE College/junior/club team = 'Molot Perm (Russia)'"}
{"text": "table: 1-1013129-1\ncolumns: Pick, Player, Position, Nationality, NHL team, College/junior/club team\nQ: The nhl team new york islanders is what nationality?\nA: SELECT Nationality FROM 1-1013129-1 WHERE NHL team = 'New York Islanders'"}
{"text": "table: 1-1013168-3\ncolumns: District, Vacator, Reason for change, Successor, Date successor seated\nQ: What is the name of the vacator for district Louisiana 1st?\nA: SELECT Vacator FROM 1-1013168-3 WHERE District = 'Louisiana 1st'"}
{"text": "table: 1-101336-1\ncolumns: Formula, Notation, T c (K), No. of Cu-O planes in unit cell, Crystal structure\nQ: What is the notion when the crystal structure is tetragonal and the formula is bi 2 sr 2 cacu 2 o 8\nA: SELECT Notation FROM 1-101336-1 WHERE Crystal structure = 'Tetragonal' AND Formula = 'Bi 2 Sr 2 CaCu 2 O 8'"}
{"text": "table: 1-101336-1\ncolumns: Formula, Notation, T c (K), No. of Cu-O planes in unit cell, Crystal structure\nQ: How many times is the formula tl 2 ba 2 cuo 6?\nA: SELECT No. of Cu-O planes in unit cell FROM 1-101336-1 WHERE Formula = 'Tl 2 Ba 2 CuO 6'"}
{"text": "table: 1-101336-1\ncolumns: Formula, Notation, T c (K), No. of Cu-O planes in unit cell, Crystal structure\nQ: What is the crystal structure for the formula yba 2 cu 3 o 7?\nA: SELECT Crystal structure FROM 1-101336-1 WHERE Formula = 'YBa 2 Cu 3 O 7'"}
{"text": "table: 1-101336-1\ncolumns: Formula, Notation, T c (K), No. of Cu-O planes in unit cell, Crystal structure\nQ: What is the number for t c (k) when the notation is tl-2212?\nA: SELECT COUNT T c (K) FROM 1-101336-1 WHERE Notation = 'Tl-2212'"}
{"text": "table: 1-10138926-1\ncolumns: #, City, 1981 Census, 1991 Census, 2001 Census, 2010 Est., Region\nQ: How many 2010 estimations have been done in the city of Cremona?\nA: SELECT COUNT 2010 Est. FROM 1-10138926-1 WHERE City = 'Cremona'"}
{"text": "table: 1-10138926-1\ncolumns: #, City, 1981 Census, 1991 Census, 2001 Census, 2010 Est., Region\nQ: What's the 2001 census of the region of Abruzzo where the 1871 census is bigger than 51092.0?\nA: SELECT MIN 2001 Census FROM 1-10138926-1 WHERE Region = 'Abruzzo' AND 1981 Census > 51092.0"}
{"text": "table: 1-10138926-1\ncolumns: #, City, 1981 Census, 1991 Census, 2001 Census, 2010 Est., Region\nQ: What's the 1991 census of the city of Carpi?\nA: SELECT MAX 1991 Census FROM 1-10138926-1 WHERE City = 'Carpi'"}
{"text": "table: 1-10138926-1\ncolumns: #, City, 1981 Census, 1991 Census, 2001 Census, 2010 Est., Region\nQ: How many 2001 censuses are there on number 13?\nA: SELECT COUNT 2001 Census FROM 1-10138926-1 WHERE # = 13"}
{"text": "table: 1-10138926-1\ncolumns: #, City, 1981 Census, 1991 Census, 2001 Census, 2010 Est., Region\nQ: What's the 1981 census of Livorno?\nA: SELECT 1981 Census FROM 1-10138926-1 WHERE City = 'Livorno'"}
{"text": "table: 1-1013129-8\ncolumns: Pick, Player, Position, Nationality, NHL team, College/junior/club team\nQ: Which NHL team has player Mike Loach?\nA: SELECT NHL team FROM 1-1013129-8 WHERE Player = 'Mike Loach'"}
{"text": "table: 1-1013129-8\ncolumns: Pick, Player, Position, Nationality, NHL team, College/junior/club team\nQ: What is the NHL team that has Peter Strom?\nA: SELECT NHL team FROM 1-1013129-8 WHERE Player = 'Peter Strom'"}
{"text": "table: 1-1013129-8\ncolumns: Pick, Player, Position, Nationality, NHL team, College/junior/club team\nQ: What team is Keith Mccambridge on?\nA: SELECT College/junior/club team FROM 1-1013129-8 WHERE Player = 'Keith McCambridge'"}
{"text": "table: 1-1013129-8\ncolumns: Pick, Player, Position, Nationality, NHL team, College/junior/club team\nQ: How many nationalities are the pick 193?\nA: SELECT COUNT Nationality FROM 1-1013129-8 WHERE Pick = 193"}
{"text": "table: 1-1013168-2\ncolumns: State (class), Vacator, Reason for change, Successor, Date of successors formal installation\nQ: Who was the succesor that was formally installed on November 8, 1978?\nA: SELECT Successor FROM 1-1013168-2 WHERE Date of successors formal installation = 'November 8, 1978'"}
{"text": "table: 1-1014319-1\ncolumns: Week, Dance/song, Horwood, Goodman, Dixon, Tonioli, Total, Result\nQ: How many songs received a 10 from Goodman and were rated by Tonioli?\nA: SELECT COUNT Tonioli FROM 1-1014319-1 WHERE Goodman = '10'"}
{"text": "table: 1-1014319-1\ncolumns: Week, Dance/song, Horwood, Goodman, Dixon, Tonioli, Total, Result\nQ: What score did Goodman give to all songs with safe results, which received a 7 from Horwood and have a total score of 31?\nA: SELECT Goodman FROM 1-1014319-1 WHERE Total = '31' AND Horwood = '7' AND Result = 'Safe'"}
{"text": "table: 1-1014319-1\ncolumns: Week, Dance/song, Horwood, Goodman, Dixon, Tonioli, Total, Result\nQ: What score did Dixon give to the song \"samba / young hearts run free\", which was in second place?\nA: SELECT Dixon FROM 1-1014319-1 WHERE Dance/song = 'Samba / Young Hearts Run Free' AND Result = 'Second place'"}
{"text": "table: 1-1014319-1\ncolumns: Week, Dance/song, Horwood, Goodman, Dixon, Tonioli, Total, Result\nQ: How many scores did Goodman give to \"samba / young hearts run free\", which was in second place?\nA: SELECT COUNT Goodman FROM 1-1014319-1 WHERE Result = 'Second place' AND Dance/song = 'Samba / Young Hearts Run Free'"}
{"text": "table: 1-1015421-1\ncolumns: Class, Operator, No. Built, Year Built, Cars per Set, Unit nos.\nQ: What year was number 7 built?\nA: SELECT Year Built FROM 1-1015421-1 WHERE No. Built = 7"}
{"text": "table: 1-1015914-24\ncolumns: Case/Suffix, we two, you and I, you two, them two (the two), who-two\nQ: What is we two when the case/suffix is loc.?\nA: SELECT we two FROM 1-1015914-24 WHERE Case/Suffix = 'loc.'"}
{"text": "table: 1-1015914-24\ncolumns: Case/Suffix, we two, you and I, you two, them two (the two), who-two\nQ: What is them two (the two) when we two is ngalbelpa?\nA: SELECT them two (the two) FROM 1-1015914-24 WHERE we two = 'ngalbelpa'"}
{"text": "table: 1-1015914-24\ncolumns: Case/Suffix, we two, you and I, you two, them two (the two), who-two\nQ: What is them two (the two) when you and i is ng\u0153balngu?\nA: SELECT them two (the two) FROM 1-1015914-24 WHERE you and I = 'ng\u0153balngu'"}
{"text": "table: 1-1015914-24\ncolumns: Case/Suffix, we two, you and I, you two, them two (the two), who-two\nQ: What is who-two where you and i is ng\u0153ban?\nA: SELECT who-two FROM 1-1015914-24 WHERE you and I = 'ng\u0153ban'"}
{"text": "table: 1-1015914-24\ncolumns: Case/Suffix, we two, you and I, you two, them two (the two), who-two\nQ: What is we two where you two is ngipen?\nA: SELECT we two FROM 1-1015914-24 WHERE you two = 'ngipen'"}
{"text": "table: 1-1015914-24\ncolumns: Case/Suffix, we two, you and I, you two, them two (the two), who-two\nQ: What is who-two when you two is ngipelngu?\nA: SELECT who-two FROM 1-1015914-24 WHERE you two = 'ngipelngu'"}
{"text": "table: 1-10160447-1\ncolumns: Position, Driver, Points, Winnings, Series\nQ: what's the\u00a0points\u00a0with\u00a0driver\u00a0 mark martin\nA: SELECT Points FROM 1-10160447-1 WHERE Driver = 'Mark Martin'"}
{"text": "table: 1-10160447-1\ncolumns: Position, Driver, Points, Winnings, Series\nQ: what's the\u00a0points\u00a0with\u00a0driver\u00a0 rusty wallace\nA: SELECT Points FROM 1-10160447-1 WHERE Driver = 'Rusty Wallace'"}
{"text": "table: 1-10160447-1\ncolumns: Position, Driver, Points, Winnings, Series\nQ: what's the total number of\u00a0position\u00a0with\u00a0driver\u00a0 robby gordon\nA: SELECT COUNT Position FROM 1-10160447-1 WHERE Driver = 'Robby Gordon'"}
{"text": "table: 1-10160447-1\ncolumns: Position, Driver, Points, Winnings, Series\nQ: what's the maximum\u00a0position\u00a0with\u00a0winnings\u00a0 $50,000\nA: SELECT MAX Position FROM 1-10160447-1 WHERE Winnings = '$50,000'"}
{"text": "table: 1-10236830-6\ncolumns: Nomination, Actors Name, Film Name, Director, Country\nQ: What actor was nominted for an award in the film Anastasiya Slutskaya?\nA: SELECT Actors Name FROM 1-10236830-6 WHERE Film Name = 'Anastasiya Slutskaya'"}
{"text": "table: 1-10236830-6\ncolumns: Nomination, Actors Name, Film Name, Director, Country\nQ: What was the film Falling up nominated for?\nA: SELECT Nomination FROM 1-10236830-6 WHERE Film Name = 'Falling Up'"}
{"text": "table: 1-10236830-6\ncolumns: Nomination, Actors Name, Film Name, Director, Country\nQ: What is the name of the actress that was nominated for best actress in a leading role in the film Chopin: Desire for love?\nA: SELECT Actors Name FROM 1-10236830-6 WHERE Film Name = 'Chopin: Desire for Love' AND Nomination = 'Best Actress in a Leading Role'"}
{"text": "table: 1-10236830-6\ncolumns: Nomination, Actors Name, Film Name, Director, Country\nQ: What is the name of the actress that was nominated for best actress in a leading role in the film Chopin: Desire for love?\nA: SELECT Actors Name FROM 1-10236830-6 WHERE Film Name = 'Chopin: Desire for Love' AND Nomination = 'Best Actress in a Leading Role'"}
{"text": "table: 1-10236830-6\ncolumns: Nomination, Actors Name, Film Name, Director, Country\nQ: Which films does the actor Alla Sergiyko star in?\nA: SELECT Film Name FROM 1-10236830-6 WHERE Actors Name = 'Alla Sergiyko'"}
{"text": "table: 1-10236830-6\ncolumns: Nomination, Actors Name, Film Name, Director, Country\nQ: Which nominations was the film 27 Stolen Kisses nominated for?\nA: SELECT Nomination FROM 1-10236830-6 WHERE Film Name = '27 Stolen Kisses'"}
{"text": "table: 1-10236830-4\ncolumns: Nomination, Actors Name, Film Name, Director, Country\nQ: Which actor from Serbia was nominated for best actor in a supporting role?\nA: SELECT Actors Name FROM 1-10236830-4 WHERE Nomination = 'Best Actor in a Supporting Role' AND Country = 'Serbia'"}
{"text": "table: 1-10236830-4\ncolumns: Nomination, Actors Name, Film Name, Director, Country\nQ: Vsevolod Shilovskiy is from what country?\nA: SELECT Country FROM 1-10236830-4 WHERE Actors Name = 'Vsevolod Shilovskiy'"}
{"text": "table: 1-10236830-4\ncolumns: Nomination, Actors Name, Film Name, Director, Country\nQ: Which nominations are connected to the film Totalitarian Romance?\nA: SELECT Nomination FROM 1-10236830-4 WHERE Film Name = 'Totalitarian Romance'"}
{"text": "table: 1-10236830-4\ncolumns: Nomination, Actors Name, Film Name, Director, Country\nQ: Srdjan Dragojevic worked on a film which earned what nomination?\nA: SELECT Nomination FROM 1-10236830-4 WHERE Director = 'Srdjan Dragojevic'"}
{"text": "table: 1-10236830-4\ncolumns: Nomination, Actors Name, Film Name, Director, Country\nQ: Which actors are from Ukraine?\nA: SELECT Actors Name FROM 1-10236830-4 WHERE Country = 'Ukraine'"}
{"text": "table: 1-10236830-1\ncolumns: Nomination, Actors Name, Film Name, Director, Country\nQ: What was the film that vadim ilyenko directed?\nA: SELECT Film Name FROM 1-10236830-1 WHERE Director = 'Vadim Ilyenko'"}
{"text": "table: 1-10236830-1\ncolumns: Nomination, Actors Name, Film Name, Director, Country\nQ: What was the actors name that vadim ilyenko directed?\nA: SELECT Actors Name FROM 1-10236830-1 WHERE Director = 'Vadim Ilyenko'"}
{"text": "table: 1-10236830-1\ncolumns: Nomination, Actors Name, Film Name, Director, Country\nQ: What was the actors name for fuchzhou and nomination was best non-professional actor?\nA: SELECT Actors Name FROM 1-10236830-1 WHERE Film Name = 'Fuchzhou' AND Nomination = 'Best Non-Professional Actor'"}
{"text": "table: 1-10236830-1\ncolumns: Nomination, Actors Name, Film Name, Director, Country\nQ: What film did michaylo ilyenko make with best actor in a supporting role?\nA: SELECT Film Name FROM 1-10236830-1 WHERE Director = 'Michaylo Ilyenko' AND Nomination = 'Best Actor in a Supporting Role'"}
{"text": "table: 1-10236830-1\ncolumns: Nomination, Actors Name, Film Name, Director, Country\nQ: What was the actor's name for best debut?\nA: SELECT Actors Name FROM 1-10236830-1 WHERE Nomination = 'Best Debut'"}
{"text": "table: 1-10236830-1\ncolumns: Nomination, Actors Name, Film Name, Director, Country\nQ: What was the number of nominations for natalia raskokoha?\nA: SELECT COUNT Nomination FROM 1-10236830-1 WHERE Actors Name = 'Natalia Raskokoha'"}
{"text": "table: 1-10240125-1\ncolumns: Season, Division, League Apps, League Goals, FA Cup Apps, FA Cup Goals, Total Apps, Total Goals\nQ: What is the highest value of Total Goals?\nA: SELECT MAX Total Goals FROM 1-10240125-1"}
{"text": "table: 1-10240125-1\ncolumns: Season, Division, League Apps, League Goals, FA Cup Apps, FA Cup Goals, Total Apps, Total Goals\nQ: When FA Cup Apps is 9 what is the smallest number of FA Cup Goals?\nA: SELECT MIN FA Cup Goals FROM 1-10240125-1 WHERE FA Cup Apps = 9"}
{"text": "table: 1-10240125-1\ncolumns: Season, Division, League Apps, League Goals, FA Cup Apps, FA Cup Goals, Total Apps, Total Goals\nQ: What is the smallest number of Total Goals?\nA: SELECT MIN Total Goals FROM 1-10240125-1"}
{"text": "table: 1-10264179-2\ncolumns: Round, Circuit, Date, Pole Position, Fastest Lap, Winning Driver, Winning Team\nQ: What circuit was the race where Hideki Mutoh had the fastest lap?\nA: SELECT Circuit FROM 1-10264179-2 WHERE Fastest Lap = 'Hideki Mutoh'"}
{"text": "table: 1-10269427-3\ncolumns: Episode #, Production code, Title, Directed by, Written by, Airdate\nQ: what is the minimum production code with title \"foreign exchange problem / turn about\"\nA: SELECT MIN Production code FROM 1-10269427-3 WHERE Title = '\"Foreign Exchange Problem / Turn About\"'"}
{"text": "table: 1-10269427-3\ncolumns: Episode #, Production code, Title, Directed by, Written by, Airdate\nQ: what is the episode # for title \"the yindianapolis 500 / personality problem\"\nA: SELECT Episode # FROM 1-10269427-3 WHERE Title = '\"The Yindianapolis 500 / Personality Problem\"'"}
{"text": "table: 1-10269427-3\ncolumns: Episode #, Production code, Title, Directed by, Written by, Airdate\nQ: what is the episode # for production code 227\nA: SELECT Episode # FROM 1-10269427-3 WHERE Production code = 227"}
{"text": "table: 1-10269427-3\ncolumns: Episode #, Production code, Title, Directed by, Written by, Airdate\nQ: who directed the movie written by is sib ventress / aydrea ten bosch\nA: SELECT Directed by FROM 1-10269427-3 WHERE Written by = 'Sib Ventress / Aydrea ten Bosch'"}
{"text": "table: 1-10269427-3\ncolumns: Episode #, Production code, Title, Directed by, Written by, Airdate\nQ: what is the production code with title \"skirting the issue / moon over my yinnie\"\nA: SELECT Production code FROM 1-10269427-3 WHERE Title = '\"Skirting the Issue / Moon Over my Yinnie\"'"}
{"text": "table: 1-10240125-2\ncolumns: Season, Division, League Apps, League Goals, FA Cup Apps, FA Cup Goals, Total Apps, Total Goals\nQ: Whatis the number of total goals maximum?\nA: SELECT MAX Total Goals FROM 1-10240125-2"}
{"text": "table: 1-10262329-1\ncolumns: Assembly Type, Adhesive Type, Time(Sec), Temp (\u00b0C), Pressure\nQ: HOW MANY TEMPERATURE INTERVALS ARE POSSIBLE TO USE WITH ACRYL? \nA: SELECT COUNT Temp (\u00b0C) FROM 1-10262329-1 WHERE Adhesive Type = 'Acryl'"}
{"text": "table: 1-1028356-3\ncolumns: Outcome, Year, Championship, Surface, Partner, Opponents, Score\nQ: How many matches where played with Jim Pugh?\nA: SELECT COUNT Opponents FROM 1-1028356-3 WHERE Partner = 'Jim Pugh'"}
{"text": "table: 1-1028356-3\ncolumns: Outcome, Year, Championship, Surface, Partner, Opponents, Score\nQ: What is the score with partner Jim Pugh?\nA: SELECT Score FROM 1-1028356-3 WHERE Partner = 'Jim Pugh'"}
{"text": "table: 1-1028356-3\ncolumns: Outcome, Year, Championship, Surface, Partner, Opponents, Score\nQ: How many matched scored 3\u20136, 7\u20136(5), 6\u20133?\nA: SELECT COUNT Surface FROM 1-1028356-3 WHERE Score = '3\u20136, 7\u20136(5), 6\u20133'"}
{"text": "table: 1-1028356-3\ncolumns: Outcome, Year, Championship, Surface, Partner, Opponents, Score\nQ: What is the score of the match with partner Jim Pugh?\nA: SELECT Score FROM 1-1028356-3 WHERE Partner = 'Jim Pugh'"}
{"text": "table: 1-1028356-3\ncolumns: Outcome, Year, Championship, Surface, Partner, Opponents, Score\nQ: What year was the championship in Wimbledon (2)?\nA: SELECT Year FROM 1-1028356-3 WHERE Championship = 'Wimbledon (2)'"}
{"text": "table: 1-1028356-3\ncolumns: Outcome, Year, Championship, Surface, Partner, Opponents, Score\nQ: What is the score of the match with opponents Gretchen Magers Kelly Jones?\nA: SELECT Score FROM 1-1028356-3 WHERE Opponents = 'Gretchen Magers Kelly Jones'"}
{"text": "table: 1-10284385-1\ncolumns: Begin Date, End Date, Representative, Date of birth, House term, State served, Party, Age (years, days)\nQ: How many birthdays does Earl Hanley Beshlin have?\nA: SELECT COUNT Date of birth FROM 1-10284385-1 WHERE Representative = 'Earl Hanley Beshlin'"}
{"text": "table: 1-10284385-1\ncolumns: Begin Date, End Date, Representative, Date of birth, House term, State served, Party, Age (years, days)\nQ: Which politican party has a birthday of November 10, 1880\nA: SELECT Party FROM 1-10284385-1 WHERE Date of birth = 'November 10, 1880'"}
{"text": "table: 1-10284385-1\ncolumns: Begin Date, End Date, Representative, Date of birth, House term, State served, Party, Age (years, days)\nQ: Which representative has a birthday of January 31, 1866?\nA: SELECT Representative FROM 1-10284385-1 WHERE Date of birth = 'January 31, 1866'"}
{"text": "table: 1-10295819-1\ncolumns: Player, Current singles ranking, Current doubles ranking, First year played, Ties played, Total W\u2013L, Singles W\u2013L, Doubles W\u2013L\nQ: What is the Singles W-L for the players named  Laurynas Grigelis?\nA: SELECT Singles W\u2013L FROM 1-10295819-1 WHERE Player = 'Laurynas Grigelis'"}
{"text": "table: 1-10295819-1\ncolumns: Player, Current singles ranking, Current doubles ranking, First year played, Ties played, Total W\u2013L, Singles W\u2013L, Doubles W\u2013L\nQ: What is the Current singles ranking for the player named Mantas Bugaili\u0161kis?\nA: SELECT Current singles ranking FROM 1-10295819-1 WHERE Player = 'Mantas Bugaili\u0161kis'"}
{"text": "table: 1-10312547-1\ncolumns: Character, 1954 Broadway, 1955 broadcast, 1960 broadcast, 1979 Broadway, 1990 Broadway, 1991 Broadway, 1998 Broadway, 1999 Broadway\nQ: How many playerd Mrs. Darling in the 1999 Broadway?\nA: SELECT COUNT 1999 Broadway FROM 1-10312547-1 WHERE Character = 'Mrs. Darling'"}
{"text": "table: 1-10312547-1\ncolumns: Character, 1954 Broadway, 1955 broadcast, 1960 broadcast, 1979 Broadway, 1990 Broadway, 1991 Broadway, 1998 Broadway, 1999 Broadway\nQ: Who played Peter Pan in the 1990 Broadway?\nA: SELECT 1990 Broadway FROM 1-10312547-1 WHERE Character = 'Peter Pan'"}
{"text": "table: 1-10312547-1\ncolumns: Character, 1954 Broadway, 1955 broadcast, 1960 broadcast, 1979 Broadway, 1990 Broadway, 1991 Broadway, 1998 Broadway, 1999 Broadway\nQ: Who played in the 1991 Broadway before Barbara McCulloh played the part in the 1999 Broadway?\nA: SELECT 1991 Broadway FROM 1-10312547-1 WHERE 1999 Broadway = 'Barbara McCulloh'"}
{"text": "table: 1-10312547-1\ncolumns: Character, 1954 Broadway, 1955 broadcast, 1960 broadcast, 1979 Broadway, 1990 Broadway, 1991 Broadway, 1998 Broadway, 1999 Broadway\nQ: Who played in the 1990 Broadway after Tom Halloran played the character in the 1955 Broadcast?\nA: SELECT 1990 Broadway FROM 1-10312547-1 WHERE 1955 broadcast = 'Tom Halloran'"}
{"text": "table: 1-10312547-1\ncolumns: Character, 1954 Broadway, 1955 broadcast, 1960 broadcast, 1979 Broadway, 1990 Broadway, 1991 Broadway, 1998 Broadway, 1999 Broadway\nQ: What character did Drake English play in the 1998 Broadway?\nA: SELECT Character FROM 1-10312547-1 WHERE 1998 Broadway = 'Drake English'"}
{"text": "table: 1-103084-4\ncolumns: Year, Broadcast date, BBC One total viewing, BBC One Rank, BBC Two total viewing, BBC Two Rank\nQ: What date was BBC One total viewing greater then 11616996.338225884?\nA: SELECT Broadcast date FROM 1-103084-4 WHERE BBC One total viewing > 11616996.338225884"}
{"text": "table: 1-103084-4\ncolumns: Year, Broadcast date, BBC One total viewing, BBC One Rank, BBC Two total viewing, BBC Two Rank\nQ: How many years did BBC One rank 20th?\nA: SELECT COUNT Year FROM 1-103084-4 WHERE BBC One Rank = '20th'"}
{"text": "table: 1-103084-4\ncolumns: Year, Broadcast date, BBC One total viewing, BBC One Rank, BBC Two total viewing, BBC Two Rank\nQ: What year was the BBC two total viewing 7,530,000?\nA: SELECT MIN Year FROM 1-103084-4 WHERE BBC Two total viewing = '7,530,000'"}
{"text": "table: 1-10321124-1\ncolumns: \u2193 Function / Genus \u2192, Shigella, Salmonella, Yersinia, Escherichia\nQ:  how many\u00a0\u2193 function / genus \u2192\u00a0with\u00a0escherichia\u00a0being espd\nA: SELECT COUNT \u2193 Function / Genus \u2192 FROM 1-10321124-1 WHERE Escherichia = 'EspD'"}
{"text": "table: 1-10321124-1\ncolumns: \u2193 Function / Genus \u2192, Shigella, Salmonella, Yersinia, Escherichia\nQ: what's the\u00a0salmonella\u00a0with\u00a0escherichia\u00a0being espd\nA: SELECT Salmonella FROM 1-10321124-1 WHERE Escherichia = 'EspD'"}
{"text": "table: 1-10321124-1\ncolumns: \u2193 Function / Genus \u2192, Shigella, Salmonella, Yersinia, Escherichia\nQ: what's the\u00a0\u2193 function / genus \u2192\u00a0with\u00a0shigella\u00a0being spa32\nA: SELECT \u2193 Function / Genus \u2192 FROM 1-10321124-1 WHERE Shigella = 'Spa32'"}
{"text": "table: 1-10321124-1\ncolumns: \u2193 Function / Genus \u2192, Shigella, Salmonella, Yersinia, Escherichia\nQ: what's the\u00a0salmonella\u00a0with\u00a0shigella\u00a0being ipgc\nA: SELECT Salmonella FROM 1-10321124-1 WHERE Shigella = 'IpgC'"}
{"text": "table: 1-10321124-1\ncolumns: \u2193 Function / Genus \u2192, Shigella, Salmonella, Yersinia, Escherichia\nQ: what's the\u00a0salmonella\u00a0with\u00a0escherichia\u00a0being sepb (escn)\nA: SELECT Salmonella FROM 1-10321124-1 WHERE Escherichia = 'SepB (EscN)'"}
{"text": "table: 1-10321124-1\ncolumns: \u2193 Function / Genus \u2192, Shigella, Salmonella, Yersinia, Escherichia\nQ: what's the\u00a0shigella\u00a0with\u00a0yersinia\u00a0being yscp\nA: SELECT Shigella FROM 1-10321124-1 WHERE Yersinia = 'YscP'"}
{"text": "table: 1-10321805-1\ncolumns: Year (Ceremony), Film title used in nomination, Original title, Director, Result\nQ: How many original titles did Marriage Italian-Style have? \nA: SELECT COUNT Original title FROM 1-10321805-1 WHERE Film title used in nomination = 'Marriage Italian-Style'"}
{"text": "table: 1-10321805-1\ncolumns: Year (Ceremony), Film title used in nomination, Original title, Director, Result\nQ: What year was a movie with the original title La Leggenda del Santo Bevitore submitted?\nA: SELECT Year (Ceremony) FROM 1-10321805-1 WHERE Original title = 'La leggenda del santo bevitore'"}
{"text": "table: 1-10335-1\ncolumns: Camp, Estimated deaths, Operational, Occupied territory, Current country of location, Primary means for mass killings\nQ: what's the\u00a0camp\u00a0with\u00a0estimated deaths\u00a0of 600,000\nA: SELECT Camp FROM 1-10335-1 WHERE Estimated deaths = '600,000'"}
{"text": "table: 1-10335-1\ncolumns: Camp, Estimated deaths, Operational, Occupied territory, Current country of location, Primary means for mass killings\nQ: what's the\u00a0operational period\u00a0with\u00a0camp\u00a0 sajmi\u0161te\nA: SELECT Operational FROM 1-10335-1 WHERE Camp = 'Sajmi\u0161te'"}
{"text": "table: 1-10335-1\ncolumns: Camp, Estimated deaths, Operational, Occupied territory, Current country of location, Primary means for mass killings\nQ: what's the\u00a0estimated deaths\u00a0with\u00a0operational period\u00a0of 17 march 1942 \u2013 end of june 1943\nA: SELECT Estimated deaths FROM 1-10335-1 WHERE Operational = '17 March 1942 \u2013 end of June 1943'"}
{"text": "table: 1-10335-1\ncolumns: Camp, Estimated deaths, Operational, Occupied territory, Current country of location, Primary means for mass killings\nQ: what's the\u00a0current country of location\u00a0with\u00a0operational period\u00a0\u00a0of summer of 1941 to 28 june 1944\nA: SELECT Current country of location FROM 1-10335-1 WHERE Operational = 'Summer of 1941 to 28 June 1944'"}
{"text": "table: 1-10335-1\ncolumns: Camp, Estimated deaths, Operational, Occupied territory, Current country of location, Primary means for mass killings\nQ: what's the\u00a0occupied territory\u00a0with\u00a0estimated deaths\u00a0of 600,000\nA: SELECT Occupied territory FROM 1-10335-1 WHERE Estimated deaths = '600,000'"}
{"text": "table: 1-10335-1\ncolumns: Camp, Estimated deaths, Operational, Occupied territory, Current country of location, Primary means for mass killings\nQ: what's the\u00a0occupied territory\u00a0with\u00a0operational\u00a0period of may 1940 \u2013 january 1945\nA: SELECT Occupied territory FROM 1-10335-1 WHERE Operational = 'May 1940 \u2013 January 1945'"}
{"text": "table: 1-10360823-1\ncolumns: Round, Choice, Overall, Player name, Position, College\nQ: Which overall pick was traded to the Cleveland Browns?\nA: SELECT Overall FROM 1-10360823-1 WHERE College = 'Traded to the Cleveland Browns'"}
{"text": "table: 1-10360823-1\ncolumns: Round, Choice, Overall, Player name, Position, College\nQ: Overall pick 240 was a pick in which round?\nA: SELECT Round FROM 1-10360823-1 WHERE Overall = 240"}
{"text": "table: 1-10360823-1\ncolumns: Round, Choice, Overall, Player name, Position, College\nQ: Which overall pick number went to college at Youngstown State?\nA: SELECT MIN Overall FROM 1-10360823-1 WHERE College = 'Youngstown State'"}
{"text": "table: 1-10360823-1\ncolumns: Round, Choice, Overall, Player name, Position, College\nQ: What position is played by pick 255 overall?\nA: SELECT Position FROM 1-10360823-1 WHERE Overall = 255"}
{"text": "table: 1-10360823-1\ncolumns: Round, Choice, Overall, Player name, Position, College\nQ: Which player was chosen in round 17?\nA: SELECT Player name FROM 1-10360823-1 WHERE Round = 17"}
{"text": "table: 1-10361453-2\ncolumns: Game, Date, Opponent, Result, Vikings points, Opponents, Record, Attendance\nQ: The record of 7-3 had the largest attendance of what?\nA: SELECT MAX Attendance FROM 1-10361453-2 WHERE Record = '7-3'"}
{"text": "table: 1-10361453-2\ncolumns: Game, Date, Opponent, Result, Vikings points, Opponents, Record, Attendance\nQ: The record of 9-4 was against which opponent?\nA: SELECT Opponent FROM 1-10361453-2 WHERE Record = '9-4'"}
{"text": "table: 1-10361453-2\ncolumns: Game, Date, Opponent, Result, Vikings points, Opponents, Record, Attendance\nQ: The game number of 8 had a record of what?\nA: SELECT Record FROM 1-10361453-2 WHERE Game = 8"}
{"text": "table: 1-10360656-1\ncolumns: Round, Choice, Overall, Player name, Position, College\nQ: What round was Steve Stonebreaker drafted?\nA: SELECT MAX Round FROM 1-10360656-1 WHERE Player name = 'Steve Stonebreaker'"}
{"text": "table: 1-10360656-1\ncolumns: Round, Choice, Overall, Player name, Position, College\nQ: Who was the top picki n the draft?\nA: SELECT MIN Choice FROM 1-10360656-1"}
{"text": "table: 1-10360656-1\ncolumns: Round, Choice, Overall, Player name, Position, College\nQ: What round was Bill Hill drafted?\nA: SELECT Choice FROM 1-10360656-1 WHERE Player name = 'Bill Hill'"}
{"text": "table: 1-10360656-1\ncolumns: Round, Choice, Overall, Player name, Position, College\nQ: What was the name of the quarterback drafted?\nA: SELECT Player name FROM 1-10360656-1 WHERE Position = 'Quarterback'"}
{"text": "table: 1-10361625-1\ncolumns: Round, Choice, Overall, Player name, Position, College\nQ: Where is the college where Keith Hartwig plays?\nA: SELECT College FROM 1-10361625-1 WHERE Player name = 'Keith Hartwig'"}
{"text": "table: 1-10361625-1\ncolumns: Round, Choice, Overall, Player name, Position, College\nQ: What is the name of the linebacker at Illinois college?\nA: SELECT Player name FROM 1-10361625-1 WHERE Position = 'Linebacker' AND College = 'Illinois'"}
{"text": "table: 1-10361625-1\ncolumns: Round, Choice, Overall, Player name, Position, College\nQ: What is the greatest round of overall 83?\nA: SELECT MAX Round FROM 1-10361625-1 WHERE Overall = 83"}
{"text": "table: 1-10361625-1\ncolumns: Round, Choice, Overall, Player name, Position, College\nQ: Which round did Tommy Kramer play in>\nA: SELECT Round FROM 1-10361625-1 WHERE Player name = 'Tommy Kramer'"}
{"text": "table: 1-10361625-1\ncolumns: Round, Choice, Overall, Player name, Position, College\nQ: What is Rice's collage score?\nA: SELECT Overall FROM 1-10361625-1 WHERE College = 'Rice'"}
{"text": "table: 1-10361230-1\ncolumns: Round, Choice, Overall, Player name, Position, College\nQ: Where does the defensive back position appear first?\nA: SELECT MIN Round FROM 1-10361230-1 WHERE Position = 'Defensive Back'"}
{"text": "table: 1-10361230-1\ncolumns: Round, Choice, Overall, Player name, Position, College\nQ: What is Bruce Cerone overall?\nA: SELECT MIN Overall FROM 1-10361230-1 WHERE Player name = 'Bruce Cerone'"}
{"text": "table: 1-10361230-1\ncolumns: Round, Choice, Overall, Player name, Position, College\nQ: Which player went to Emporia State?\nA: SELECT Player name FROM 1-10361230-1 WHERE College = 'Emporia State'"}
{"text": "table: 1-10361230-1\ncolumns: Round, Choice, Overall, Player name, Position, College\nQ: What is the highest choice?\nA: SELECT MAX Choice FROM 1-10361230-1"}
{"text": "table: 1-10361230-1\ncolumns: Round, Choice, Overall, Player name, Position, College\nQ: What college did Bill Cappleman go to?\nA: SELECT College FROM 1-10361230-1 WHERE Player name = 'Bill Cappleman'"}
{"text": "table: 1-1036189-1\ncolumns: Headstamp ID, Primer Annulus Color, Bullet Tip Color, Other Features, Functional Type\nQ: For the headstamp id of h2, what was the color of the bullet tip?\nA: SELECT Bullet Tip Color FROM 1-1036189-1 WHERE Headstamp ID = 'H2'"}
{"text": "table: 1-1036189-1\ncolumns: Headstamp ID, Primer Annulus Color, Bullet Tip Color, Other Features, Functional Type\nQ: For the functional type of light ball, what were the other features?\nA: SELECT Other Features FROM 1-1036189-1 WHERE Functional Type = 'Light Ball'"}
{"text": "table: 1-1036189-1\ncolumns: Headstamp ID, Primer Annulus Color, Bullet Tip Color, Other Features, Functional Type\nQ: How many primers annulus colors were there when the color of the bullet tip was white?\nA: SELECT COUNT Primer Annulus Color FROM 1-1036189-1 WHERE Bullet Tip Color = 'White'"}
{"text": "table: 1-1036189-1\ncolumns: Headstamp ID, Primer Annulus Color, Bullet Tip Color, Other Features, Functional Type\nQ: How many bullet tips colors had other features of a blue band on case base?\nA: SELECT COUNT Bullet Tip Color FROM 1-1036189-1 WHERE Other Features = 'Blue band on case base'"}
{"text": "table: 1-1037590-1\ncolumns: Year, Games, Games started, Completions, Attempts, Completion %, Yards, Yards/Attempt, Touchdowns, Interceptions, Rating\nQ: How many touchdowns were scored in the year with a completion percentage of 56.0?\nA: SELECT MIN Touchdowns FROM 1-1037590-1 WHERE Completion % = '56.0'"}
{"text": "table: 1-1037590-1\ncolumns: Year, Games, Games started, Completions, Attempts, Completion %, Yards, Yards/Attempt, Touchdowns, Interceptions, Rating\nQ: What number of completions are recorded for the year with 12 games started?\nA: SELECT Completions FROM 1-1037590-1 WHERE Games started = 12"}
{"text": "table: 1-1037590-1\ncolumns: Year, Games, Games started, Completions, Attempts, Completion %, Yards, Yards/Attempt, Touchdowns, Interceptions, Rating\nQ: How many years were there with 348 attempts?\nA: SELECT COUNT Yards FROM 1-1037590-1 WHERE Attempts = 348"}
{"text": "table: 1-10402018-1\ncolumns: Character, Australia & New Zealand (Sydney - first run, Melbourne, Auckland), London, Toronto / Broadway, Brazil, UK Tour, US Tour, Italy (Milan, Rome, Trieste)\nQ: How many characters is by Babs Rubenstein?\nA: SELECT COUNT London FROM 1-10402018-1 WHERE US Tour = 'Babs Rubenstein'"}
{"text": "table: 1-10402018-1\ncolumns: Character, Australia & New Zealand (Sydney - first run, Melbourne, Auckland), London, Toronto / Broadway, Brazil, UK Tour, US Tour, Italy (Milan, Rome, Trieste)\nQ: Which person is in the tronto/broadway and has a uk tour of n/a\nA: SELECT Toronto / Broadway FROM 1-10402018-1 WHERE UK Tour = 'n/a'"}
{"text": "table: 1-10402018-1\ncolumns: Character, Australia & New Zealand (Sydney - first run, Melbourne, Auckland), London, Toronto / Broadway, Brazil, UK Tour, US Tour, Italy (Milan, Rome, Trieste)\nQ: How many people play Frank in London?\nA: SELECT COUNT London FROM 1-10402018-1 WHERE Character = 'Frank'"}
{"text": "table: 1-10399701-2\ncolumns: School Year, Class A, Class AA, Class AAA, Class AAAA, Class AAAAA\nQ: Who was Class AAA during the school year of 2000-01?\nA: SELECT Class AAA FROM 1-10399701-2 WHERE School Year = '2000-01'"}
{"text": "table: 1-10399701-2\ncolumns: School Year, Class A, Class AA, Class AAA, Class AAAA, Class AAAAA\nQ: Who was Class AAA during the same year that Class A was (tie) Apple Springs/Texline?\nA: SELECT Class AAA FROM 1-10399701-2 WHERE Class A = '(tie) Apple Springs/Texline'"}
{"text": "table: 1-10399701-2\ncolumns: School Year, Class A, Class AA, Class AAA, Class AAAA, Class AAAAA\nQ: Who was Class AAAAA during the school year of 1995-96?\nA: SELECT Class AAAAA FROM 1-10399701-2 WHERE School Year = '1995-96'"}
{"text": "table: 1-10399701-2\ncolumns: School Year, Class A, Class AA, Class AAA, Class AAAA, Class AAAAA\nQ: Who was Class AAA during the same year that Class AAAAA was Brownsville Pace?\nA: SELECT Class AAA FROM 1-10399701-2 WHERE Class AAAAA = 'Brownsville Pace'"}
{"text": "table: 1-10399701-2\ncolumns: School Year, Class A, Class AA, Class AAA, Class AAAA, Class AAAAA\nQ: What was the total number of Class AAA during the same year that Class AAA was White Oak?\nA: SELECT COUNT Class AA FROM 1-10399701-2 WHERE Class AAA = 'White Oak'"}
{"text": "table: 1-********-2\ncolumns: Week, Date, Kickoff, Opponent, Final score, Team record, Game site, Attendance\nQ: How many records are listed on Friday, May 25?\nA: SELECT COUNT Team record FROM 1-********-2 WHERE Date = 'Friday, May 25'"}
{"text": "table: 1-********-2\ncolumns: Week, Date, Kickoff, Opponent, Final score, Team record, Game site, Attendance\nQ: How many opponents were played on Saturday, June 9?\nA: SELECT COUNT Opponent FROM 1-********-2 WHERE Date = 'Saturday, June 9'"}
{"text": "table: 1-********-2\ncolumns: Week, Date, Kickoff, Opponent, Final score, Team record, Game site, Attendance\nQ: In what week was the first game played at the Commerzbank-Arena?\nA: SELECT MIN Week FROM 1-********-2 WHERE Game site = 'Commerzbank-Arena'"}
{"text": "table: 1-********-5\ncolumns: No. in series, No. in season, Title, Setting, Directed by, Written by, U.S. viewers (million), Original air date\nQ: What was the original air date of an episode set in 1544?\nA: SELECT Original air date FROM 1-********-5 WHERE Setting = '1544'"}
{"text": "table: 1-********-5\ncolumns: No. in series, No. in season, Title, Setting, Directed by, Written by, U.S. viewers (million), Original air date\nQ: How many settings where there for episode 29 of the season?\nA: SELECT COUNT Setting FROM 1-********-5 WHERE No. in series = 29"}
{"text": "table: 1-********-5\ncolumns: No. in series, No. in season, Title, Setting, Directed by, Written by, U.S. viewers (million), Original air date\nQ: Who wrote the episode that was set in winter 1541/february 13, 1542?\nA: SELECT Written by FROM 1-********-5 WHERE Setting = 'Winter 1541/February 13, 1542'"}
{"text": "table: 1-********-4\ncolumns: No. in series, No. in season, Title, Setting, Directed by, Written by, Original air date\nQ: What episode number of the season was \"The Northern Uprising\"?\nA: SELECT No. in season FROM 1-********-4 WHERE Title = '\"The Northern Uprising\"'"}
{"text": "table: 1-10416547-1\ncolumns: Date, Album name, Track, Track title, Lyricist, Music genre/style, Major instrument(s), Lyrics theme/style, Duration\nQ: What is the name of the track that lasts 5:30?\nA: SELECT Track title FROM 1-10416547-1 WHERE Duration = '5:30'"}
{"text": "table: 1-10416547-1\ncolumns: Date, Album name, Track, Track title, Lyricist, Music genre/style, Major instrument(s), Lyrics theme/style, Duration\nQ: What is the album namethat has the track title Sweetness \u751c\u751c\u7684 (ti\u00e1n ti\u00e1n de)?\nA: SELECT Album name FROM 1-10416547-1 WHERE Track title = 'Sweetness \u751c\u751c\u7684 (Ti\u00e1n ti\u00e1n de)'"}
{"text": "table: 1-10416547-1\ncolumns: Date, Album name, Track, Track title, Lyricist, Music genre/style, Major instrument(s), Lyrics theme/style, Duration\nQ: What is the duration of the song where the major instrument is the piano and the date is 2004-02-03?\nA: SELECT Duration FROM 1-10416547-1 WHERE Major instrument(s) = 'Piano' AND Date = '2004-02-03'"}
{"text": "table: 1-10416547-1\ncolumns: Date, Album name, Track, Track title, Lyricist, Music genre/style, Major instrument(s), Lyrics theme/style, Duration\nQ: What is the total number of lyricist where the lyrics theme is romance and the song lasts 3:50?\nA: SELECT COUNT Lyricist FROM 1-10416547-1 WHERE Lyrics theme/style = 'Romance' AND Duration = '3:50'"}
{"text": "table: 1-10416547-1\ncolumns: Date, Album name, Track, Track title, Lyricist, Music genre/style, Major instrument(s), Lyrics theme/style, Duration\nQ: What is the major instrument of the song that lasts 4:32? \nA: SELECT Major instrument(s) FROM 1-10416547-1 WHERE Duration = '4:32'"}
{"text": "table: 1-10416547-1\ncolumns: Date, Album name, Track, Track title, Lyricist, Music genre/style, Major instrument(s), Lyrics theme/style, Duration\nQ: What is the total number of music genre/style in which the lyrics are a detective story?\nA: SELECT COUNT Music genre/style FROM 1-10416547-1 WHERE Lyrics theme/style = 'Detective story'"}
{"text": "table: 1-1046071-1\ncolumns: Year, Division, League, Regular Season, Playoffs, Open Cup\nQ: What is the playoffs for the usl pro select league?\nA: SELECT Playoffs FROM 1-1046071-1 WHERE League = 'USL Pro Select League'"}
{"text": "table: 1-1046071-1\ncolumns: Year, Division, League, Regular Season, Playoffs, Open Cup\nQ: What is the number of the division for the 1st round?\nA: SELECT COUNT Division FROM 1-1046071-1 WHERE Open Cup = '1st Round'"}
{"text": "table: 1-10420426-1\ncolumns: Season, Series, Team, Races, Wins, Poles, F/Laps, Podiums, Points, Position\nQ: What was the team where series is formula renault 2.0 nec?\nA: SELECT Team FROM 1-10420426-1 WHERE Series = 'Formula Renault 2.0 NEC'"}
{"text": "table: 1-10420426-1\ncolumns: Season, Series, Team, Races, Wins, Poles, F/Laps, Podiums, Points, Position\nQ: What is the total number of poles for arden international?\nA: SELECT COUNT Poles FROM 1-10420426-1 WHERE Team = 'Arden International'"}
{"text": "table: 1-10420426-1\ncolumns: Season, Series, Team, Races, Wins, Poles, F/Laps, Podiums, Points, Position\nQ: What is the number of wins for gp2 series for racing engineering?\nA: SELECT COUNT Wins FROM 1-10420426-1 WHERE Series = 'GP2 Series' AND Team = 'Racing Engineering'"}
{"text": "table: 1-10420426-1\ncolumns: Season, Series, Team, Races, Wins, Poles, F/Laps, Podiums, Points, Position\nQ: What is the number of podiums for season 2010 for campionato italiano superstars.\nA: SELECT COUNT Podiums FROM 1-10420426-1 WHERE Season = '2010' AND Series = 'Campionato Italiano Superstars'"}
{"text": "table: 1-10420426-1\ncolumns: Season, Series, Team, Races, Wins, Poles, F/Laps, Podiums, Points, Position\nQ: What is the podium for 144 points?\nA: SELECT Podiums FROM 1-10420426-1 WHERE Points = 144"}
{"text": "table: 1-10470082-3\ncolumns: No., #, Title, Director, Writer, US air date, Storyteller, Villains\nQ: How many writers had an US air date of september 25, 1993?\nA: SELECT COUNT Writer FROM 1-10470082-3 WHERE US air date = 'September 25, 1993'"}
{"text": "table: 1-10470082-3\ncolumns: No., #, Title, Director, Writer, US air date, Storyteller, Villains\nQ: How many villians were in No. 25?\nA: SELECT COUNT Villains FROM 1-10470082-3 WHERE No. = 25"}
{"text": "table: 1-1046454-1\ncolumns: Year, Division, League, Regular Season, Playoffs, Open Cup\nQ: what being the maximum\u00a0year\u00a0where\u00a0regular season\u00a0is 4th, northwest\nA: SELECT MAX Year FROM 1-1046454-1 WHERE Regular Season = '4th, Northwest'"}
{"text": "table: 1-1046454-1\ncolumns: Year, Division, League, Regular Season, Playoffs, Open Cup\nQ: what is the total number of\u00a0playoffs\u00a0where\u00a0regular season\u00a0is 6th, southwest\nA: SELECT COUNT Playoffs FROM 1-1046454-1 WHERE Regular Season = '6th, Southwest'"}
{"text": "table: 1-1046454-1\ncolumns: Year, Division, League, Regular Season, Playoffs, Open Cup\nQ: what is the maximum\u00a0division\nA: SELECT MAX Division FROM 1-1046454-1"}
{"text": "table: 1-1046454-1\ncolumns: Year, Division, League, Regular Season, Playoffs, Open Cup\nQ:  what's the\u00a0league\u00a0where\u00a0regular season\u00a0is 2nd, northwest\nA: SELECT League FROM 1-1046454-1 WHERE Regular Season = '2nd, Northwest'"}
{"text": "table: 1-1046454-1\ncolumns: Year, Division, League, Regular Season, Playoffs, Open Cup\nQ: what are all the regular season where year is 2011\nA: SELECT Regular Season FROM 1-1046454-1 WHERE Year = 2011"}
{"text": "table: 1-10470082-5\ncolumns: No., #, Title, Director, Writer, US air date, Storyteller, Villains\nQ: How many titles have the number 11\nA: SELECT COUNT Title FROM 1-10470082-5 WHERE # = 11"}
{"text": "table: 1-10470082-5\ncolumns: No., #, Title, Director, Writer, US air date, Storyteller, Villains\nQ: How many have Mrs. briar as a villain\nA: SELECT COUNT No. FROM 1-10470082-5 WHERE Villains = 'Mrs. Briar'"}
{"text": "table: 1-10470082-5\ncolumns: No., #, Title, Director, Writer, US air date, Storyteller, Villains\nQ: how many have the number 8\nA: SELECT COUNT No. FROM 1-10470082-5 WHERE # = 8"}
{"text": "table: 1-10470082-5\ncolumns: No., #, Title, Director, Writer, US air date, Storyteller, Villains\nQ: How many have the title \"the tale of the room for rent\"\nA: SELECT COUNT # FROM 1-10470082-5 WHERE Title = '\"The Tale of the Room for Rent\"'"}
{"text": "table: 1-10470082-6\ncolumns: No., #, Title, Director, Writer, US air date, Storyteller, Villains\nQ: What is the name of the episode told by Kiki and directed by Will Dixon?\nA: SELECT Title FROM 1-10470082-6 WHERE Storyteller = 'Kiki' AND Director = 'Will Dixon'"}
{"text": "table: 1-10470082-6\ncolumns: No., #, Title, Director, Writer, US air date, Storyteller, Villains\nQ: Who is the storyteller in the episode called \"The Tale of the Jagged Sign\"?\nA: SELECT Storyteller FROM 1-10470082-6 WHERE Title = '\"The Tale of the Jagged Sign\"'"}
{"text": "table: 1-10470082-6\ncolumns: No., #, Title, Director, Writer, US air date, Storyteller, Villains\nQ: Who wrote Episode #3?\nA: SELECT Writer FROM 1-10470082-6 WHERE # = 3"}
{"text": "table: 1-10470082-7\ncolumns: No., #, Title, Director, Writer, US air date, Storyteller, Villains\nQ: Who are the villains in the episode titled \"The Tale of the Forever Game\"?\nA: SELECT Villains FROM 1-10470082-7 WHERE Title = '\"The Tale of The Forever Game\"'"}
{"text": "table: 1-10470082-7\ncolumns: No., #, Title, Director, Writer, US air date, Storyteller, Villains\nQ: How many villains appeared in the episode titled \"The Tale of the Virtual Pets\"?\nA: SELECT COUNT Villains FROM 1-10470082-7 WHERE Title = '\"The Tale of The Virtual Pets\"'"}
{"text": "table: 1-10470082-7\ncolumns: No., #, Title, Director, Writer, US air date, Storyteller, Villains\nQ: Who are the villains in the episodes where Megan is the storyteller and Lorette LeBlanc is the director?\nA: SELECT Villains FROM 1-10470082-7 WHERE Storyteller = 'Megan' AND Director = 'Lorette LeBlanc'"}
{"text": "table: 1-10470082-7\ncolumns: No., #, Title, Director, Writer, US air date, Storyteller, Villains\nQ: What is the largest # for an episode that was written by Allison Lea Bingeman?\nA: SELECT MAX # FROM 1-10470082-7 WHERE Writer = 'Allison Lea Bingeman'"}
{"text": "table: 1-10477224-1\ncolumns: Sepal length, Sepal width, Petal length, Petal width, Species\nQ: Name the species when petal width is 2.0 and petal length is 4.9\nA: SELECT Species FROM 1-10477224-1 WHERE Petal width = '2.0' AND Petal length = '4.9'"}
{"text": "table: 1-10477224-1\ncolumns: Sepal length, Sepal width, Petal length, Petal width, Species\nQ: Name the sepal width for i.virginica with petal length of 5.1\nA: SELECT Sepal width FROM 1-10477224-1 WHERE Species = 'I.virginica' AND Petal length = '5.1'"}
{"text": "table: 1-10477224-1\ncolumns: Sepal length, Sepal width, Petal length, Petal width, Species\nQ: Name the number of species with sepal width of 3.4 and sepal length of 5.4\nA: SELECT COUNT Species FROM 1-10477224-1 WHERE Sepal width = '3.4' AND Sepal length = '5.4'"}
{"text": "table: 1-10477224-1\ncolumns: Sepal length, Sepal width, Petal length, Petal width, Species\nQ: Name the sepal length for sepal width of 2.8 and petal length of 5.1\nA: SELECT Sepal length FROM 1-10477224-1 WHERE Sepal width = '2.8' AND Petal length = '5.1'"}
{"text": "table: 1-10477224-1\ncolumns: Sepal length, Sepal width, Petal length, Petal width, Species\nQ: Name the sepal width when sepal length is 6.5 and petal width is 2.2\nA: SELECT Sepal width FROM 1-10477224-1 WHERE Sepal length = '6.5' AND Petal width = '2.2'"}
{"text": "table: 1-10477224-1\ncolumns: Sepal length, Sepal width, Petal length, Petal width, Species\nQ: Name the sepal lengh when sepal width is 2.9 and petal width 1.3\nA: SELECT Sepal length FROM 1-10477224-1 WHERE Sepal width = '2.9' AND Petal width = '1.3'"}
{"text": "table: 1-10470082-4\ncolumns: No., #, Title, Director, Writer, US air date, Storyteller, Villains\nQ: Who is the director and what number is the episode for episode #1 of Are You Afraid of the Dark season 3?\nA: SELECT COUNT Director FROM 1-10470082-4 WHERE # = 1"}
{"text": "table: 1-10470082-4\ncolumns: No., #, Title, Director, Writer, US air date, Storyteller, Villains\nQ: Who is the director of the episode whom Scott Peters is the writer?\nA: SELECT Director FROM 1-10470082-4 WHERE Writer = 'Scott Peters'"}
{"text": "table: 1-10470082-4\ncolumns: No., #, Title, Director, Writer, US air date, Storyteller, Villains\nQ: Who is the villain in episode #7?\nA: SELECT Villains FROM 1-10470082-4 WHERE # = 7"}
{"text": "table: 1-10470082-8\ncolumns: No., #, Title, Director, Writer, US air date, Storyteller, Villains\nQ: Who wrote episode #1 in season 7?\nA: SELECT COUNT Writer FROM 1-10470082-8 WHERE # = 1"}
{"text": "table: 1-10470082-8\ncolumns: No., #, Title, Director, Writer, US air date, Storyteller, Villains\nQ: When did the episode written by Jim Morris air?\nA: SELECT US air date FROM 1-10470082-8 WHERE Writer = 'Jim Morris'"}
{"text": "table: 1-10527215-3\ncolumns: Rd, Name, Pole Position, Fastest Lap, Winning driver, Winning team, Report\nQ: What was Datsun Twin 200's fastest lap?\nA: SELECT Fastest Lap FROM 1-10527215-3 WHERE Name = 'Datsun Twin 200'"}
{"text": "table: 1-10527215-3\ncolumns: Rd, Name, Pole Position, Fastest Lap, Winning driver, Winning team, Report\nQ: In the Datsun Twin 200 race, what was the fastest lap?\nA: SELECT Fastest Lap FROM 1-10527215-3 WHERE Name = 'Datsun Twin 200'"}
{"text": "table: 1-10527215-3\ncolumns: Rd, Name, Pole Position, Fastest Lap, Winning driver, Winning team, Report\nQ: What's the report for the True Value 500?\nA: SELECT Report FROM 1-10527215-3 WHERE Name = 'True Value 500'"}
{"text": "table: 1-10527215-3\ncolumns: Rd, Name, Pole Position, Fastest Lap, Winning driver, Winning team, Report\nQ: What was Johnny Rutherford's fastest lap while Al Unser was the pole position?\nA: SELECT Fastest Lap FROM 1-10527215-3 WHERE Winning driver = 'Johnny Rutherford' AND Pole Position = 'Al Unser'"}
{"text": "table: 1-10527215-3\ncolumns: Rd, Name, Pole Position, Fastest Lap, Winning driver, Winning team, Report\nQ: What's the report on Penske Racing winning while the pole position was Al Unser?\nA: SELECT COUNT Report FROM 1-10527215-3 WHERE Pole Position = 'Al Unser' AND Winning team = 'Penske Racing'"}
{"text": "table: 1-104858-1\ncolumns: Country, Membership (from 2010), Name of member organization, Year current Scouting organization joined WOSM, Year member organization was founded, Admits boys/girls\nQ: Which countries have a scouting organization that was founded in 1926, and joined WOSM in 1930?\nA: SELECT Country FROM 1-104858-1 WHERE Year current Scouting organization joined WOSM = '1930' AND Year member organization was founded = '1926'"}
{"text": "table: 1-104858-1\ncolumns: Country, Membership (from 2010), Name of member organization, Year current Scouting organization joined WOSM, Year member organization was founded, Admits boys/girls\nQ: Does Venezuela admit only boys, only girls, or both?\nA: SELECT Admits boys/girls FROM 1-104858-1 WHERE Country = 'Venezuela'"}
{"text": "table: 1-104858-1\ncolumns: Country, Membership (from 2010), Name of member organization, Year current Scouting organization joined WOSM, Year member organization was founded, Admits boys/girls\nQ: Which organizations were founded in 1972, but became WOSM members until 1977?\nA: SELECT Name of member organization FROM 1-104858-1 WHERE Year member organization was founded = '1972' AND Year current Scouting organization joined WOSM = '1977'"}
{"text": "table: 1-104858-1\ncolumns: Country, Membership (from 2010), Name of member organization, Year current Scouting organization joined WOSM, Year member organization was founded, Admits boys/girls\nQ: Does the Scout Association of Hong Kong admit boys, girls, or both?\nA: SELECT Admits boys/girls FROM 1-104858-1 WHERE Name of member organization = 'The Scout Association of Hong Kong'"}
{"text": "table: 1-104858-1\ncolumns: Country, Membership (from 2010), Name of member organization, Year current Scouting organization joined WOSM, Year member organization was founded, Admits boys/girls\nQ: Does the Ghana Scout Association (founded in 1912) admit boys, girls, or both?\nA: SELECT Admits boys/girls FROM 1-104858-1 WHERE Year member organization was founded = '1912' AND Name of member organization = 'The Ghana Scout Association'"}
{"text": "table: 1-10528691-4\ncolumns: Model, Introduction, Discontinued, CPU Speed, Print resolution (DPI) Resolution is given in dots per inch (DPI), Print speed (PPM), Standard memory, Maximum memory\nQ: What is the model number introduced May 1999?\nA: SELECT MAX Model FROM 1-10528691-4 WHERE Introduction = 'May 1999'"}
{"text": "table: 1-10528691-4\ncolumns: Model, Introduction, Discontinued, CPU Speed, Print resolution (DPI) Resolution is given in dots per inch (DPI), Print speed (PPM), Standard memory, Maximum memory\nQ: What is the print resolution (FPI) for December 2002?\nA: SELECT Print resolution (DPI) Resolution is given in dots per inch (DPI) FROM 1-10528691-4 WHERE Introduction = 'December 2002'"}
{"text": "table: 1-10528691-4\ncolumns: Model, Introduction, Discontinued, CPU Speed, Print resolution (DPI) Resolution is given in dots per inch (DPI), Print speed (PPM), Standard memory, Maximum memory\nQ: What is the maximum memory for the model discontinued in November 2001?\nA: SELECT Maximum memory FROM 1-10528691-4 WHERE Discontinued = 'November 2001'"}
{"text": "table: 1-1053802-1\ncolumns: Region/country, Local title, Network, Winners, Main presenters\nQ: What is main presenters of La Granja?\nA: SELECT Main presenters FROM 1-1053802-1 WHERE Local title = 'La Granja'"}
{"text": "table: 1-1053802-1\ncolumns: Region/country, Local title, Network, Winners, Main presenters\nQ: What is the main presenter of bulgaria?\nA: SELECT Main presenters FROM 1-1053802-1 WHERE Region/country = 'Bulgaria'"}
{"text": "table: 1-1053802-1\ncolumns: Region/country, Local title, Network, Winners, Main presenters\nQ: How many winners are there of farma?\nA: SELECT COUNT Winners FROM 1-1053802-1 WHERE Local title = 'Farma'"}
{"text": "table: 1-10556257-1\ncolumns: Season, Team, League Apps, League Goals, Cup Apps, Cup Goals\nQ: What is the most cup goals for seasson 1911-12?\nA: SELECT MAX Cup Goals FROM 1-10556257-1 WHERE Season = '1911-12'"}
{"text": "table: 1-10556257-1\ncolumns: Season, Team, League Apps, League Goals, Cup Apps, Cup Goals\nQ: What is the league apps for season 1923-24?\nA: SELECT League Apps FROM 1-10556257-1 WHERE Season = '1923-24'"}
{"text": "table: 1-10556257-1\ncolumns: Season, Team, League Apps, League Goals, Cup Apps, Cup Goals\nQ: What is the team for season 1911-12?\nA: SELECT Team FROM 1-10556257-1 WHERE Season = '1911-12'"}
{"text": "table: 1-10566855-1\ncolumns: Season, Premier, Runner-up, Score, Margin, Venue, Attendance\nQ: what's the minimum\u00a0attendance\u00a0with\u00a0score\u00a0 10.16 (76) \u2013 9.22 (76)\nA: SELECT MIN Attendance FROM 1-10566855-1 WHERE Score = '10.16 (76) \u2013 9.22 (76)'"}
{"text": "table: 1-10566855-1\ncolumns: Season, Premier, Runner-up, Score, Margin, Venue, Attendance\nQ: who's the\u00a0premier\u00a0with\u00a0in 1970\nA: SELECT Premier FROM 1-10566855-1 WHERE Season = 1970"}
{"text": "table: 1-10566855-1\ncolumns: Season, Premier, Runner-up, Score, Margin, Venue, Attendance\nQ: who are all the runner-up for premier in richmond\nA: SELECT Runner-up FROM 1-10566855-1 WHERE Premier = 'Richmond'"}
{"text": "table: 1-10566855-1\ncolumns: Season, Premier, Runner-up, Score, Margin, Venue, Attendance\nQ: what is the minimum attendance with score 8.16 (64) \u2013 8.12 (60)\nA: SELECT MIN Attendance FROM 1-10566855-1 WHERE Score = '8.16 (64) \u2013 8.12 (60)'"}
{"text": "table: 1-10568553-1\ncolumns: County, Location, Street Names, Milepost, Roads Intersected, Notes\nQ: How many mileposts are there on Anne Street?\nA: SELECT COUNT Milepost FROM 1-10568553-1 WHERE Street Names = 'Anne Street'"}
{"text": "table: 1-10568553-1\ncolumns: County, Location, Street Names, Milepost, Roads Intersected, Notes\nQ: Which street is 12.2 miles long?\nA: SELECT Street Names FROM 1-10568553-1 WHERE Milepost = '12.2'"}
{"text": "table: 1-10568553-1\ncolumns: County, Location, Street Names, Milepost, Roads Intersected, Notes\nQ: Where does Route 24 intersect?\nA: SELECT Location FROM 1-10568553-1 WHERE Roads Intersected = 'Route 24'"}
{"text": "table: 1-10568553-1\ncolumns: County, Location, Street Names, Milepost, Roads Intersected, Notes\nQ: Where is milepost 12.8?\nA: SELECT Location FROM 1-10568553-1 WHERE Milepost = '12.8'"}
{"text": "table: 1-1057262-1\ncolumns: Commodity, 2001-02, 2002-03, 2003-04, 2004-05, 2005-06, 2006-07\nQ: What is the minimum amount for wool for 2001-02?\nA: SELECT MIN 2001-02 FROM 1-1057262-1 WHERE Commodity = 'Wool'"}
{"text": "table: 1-1057316-1\ncolumns: Serial number, Wheel arrangement ( Whyte notation ), Build date, Operational owner(s), Disposition\nQ: Who were the operational owners during the construction date of April 1892?\nA: SELECT Operational owner(s) FROM 1-1057316-1 WHERE Build date = 'April 1892'"}
{"text": "table: 1-1057316-1\ncolumns: Serial number, Wheel arrangement ( Whyte notation ), Build date, Operational owner(s), Disposition\nQ: Where can you find Colorado and Southern Railway #9?\nA: SELECT Disposition FROM 1-1057316-1 WHERE Operational owner(s) = 'Colorado and Southern Railway #9'"}
{"text": "table: 1-1057316-1\ncolumns: Serial number, Wheel arrangement ( Whyte notation ), Build date, Operational owner(s), Disposition\nQ: What is the wheel arrangement for the train in Riverdale, Georgia?\nA: SELECT Wheel arrangement ( Whyte notation ) FROM 1-1057316-1 WHERE Disposition = 'Riverdale, Georgia'"}
{"text": "table: 1-1057316-1\ncolumns: Serial number, Wheel arrangement ( Whyte notation ), Build date, Operational owner(s), Disposition\nQ: When was the train 2053 built?\nA: SELECT Build date FROM 1-1057316-1 WHERE Serial number = '2053'"}
{"text": "table: 1-1057316-1\ncolumns: Serial number, Wheel arrangement ( Whyte notation ), Build date, Operational owner(s), Disposition\nQ: How many wheels does the train owned by Texas and New Orleans Railroad #319 have?\nA: SELECT COUNT Wheel arrangement ( Whyte notation ) FROM 1-1057316-1 WHERE Operational owner(s) = 'Texas and New Orleans Railroad #319'"}
{"text": "table: 1-10577579-3\ncolumns: Institution, Location, Men\u2019s Nickname, Women\u2019s Nickname, Founded, Type, Enrollment, Joined, Left, Current Conference, Classification\nQ: Which college has the men's nickname of the blazers?\nA: SELECT Institution FROM 1-10577579-3 WHERE Men\u2019s Nickname = 'Blazers'"}
{"text": "table: 1-10577579-3\ncolumns: Institution, Location, Men\u2019s Nickname, Women\u2019s Nickname, Founded, Type, Enrollment, Joined, Left, Current Conference, Classification\nQ: Name the joined for the wolfpack women's nickname\nA: SELECT Joined FROM 1-10577579-3 WHERE Women\u2019s Nickname = 'Wolfpack'"}
{"text": "table: 1-10577579-3\ncolumns: Institution, Location, Men\u2019s Nickname, Women\u2019s Nickname, Founded, Type, Enrollment, Joined, Left, Current Conference, Classification\nQ: Name the left of the Lady Pilots.\nA: SELECT Left FROM 1-10577579-3 WHERE Women\u2019s Nickname = 'Lady Pilots'"}
{"text": "table: 1-10577579-3\ncolumns: Institution, Location, Men\u2019s Nickname, Women\u2019s Nickname, Founded, Type, Enrollment, Joined, Left, Current Conference, Classification\nQ: Name the women's nickname when the enrollment is 1500 in mobile, Alabama.\nA: SELECT Women\u2019s Nickname FROM 1-10577579-3 WHERE Enrollment = 1500 AND Location = 'Mobile, Alabama'"}
{"text": "table: 1-10577579-3\ncolumns: Institution, Location, Men\u2019s Nickname, Women\u2019s Nickname, Founded, Type, Enrollment, Joined, Left, Current Conference, Classification\nQ: Which conference is in Jackson, Mississippi?\nA: SELECT Current Conference FROM 1-10577579-3 WHERE Location = 'Jackson, Mississippi'"}
{"text": "table: 1-10577579-3\ncolumns: Institution, Location, Men\u2019s Nickname, Women\u2019s Nickname, Founded, Type, Enrollment, Joined, Left, Current Conference, Classification\nQ: What is the men's nickname at the school that has the lady wildcats women's nickname?\nA: SELECT Men\u2019s Nickname FROM 1-10577579-3 WHERE Women\u2019s Nickname = 'Lady Wildcats'"}
{"text": "table: 1-10577579-2\ncolumns: Institution, Location, Mens Nickname, Womens Nickname, Founded, Type, Enrollment, Joined\nQ: What is the Mens Nickname for the member location of Jacksonville, florida?\nA: SELECT Mens Nickname FROM 1-10577579-2 WHERE Location = 'Jacksonville, Florida'"}
{"text": "table: 1-10577579-2\ncolumns: Institution, Location, Mens Nickname, Womens Nickname, Founded, Type, Enrollment, Joined\nQ: What is the enrollment for the institution that was founded in 1866 and is a private/(african methodist) type?\nA: SELECT MAX Enrollment FROM 1-10577579-2 WHERE Founded = 1866 AND Type = 'Private/(African Methodist)'"}
{"text": "table: 1-10577579-2\ncolumns: Institution, Location, Mens Nickname, Womens Nickname, Founded, Type, Enrollment, Joined\nQ: That is the year founded for the institution location of Nashville, Tennessee?\nA: SELECT MIN Founded FROM 1-10577579-2 WHERE Location = 'Nashville, Tennessee'"}
{"text": "table: 1-10577579-2\ncolumns: Institution, Location, Mens Nickname, Womens Nickname, Founded, Type, Enrollment, Joined\nQ: What is the year the institution Tougaloo College joined?\nA: SELECT Joined FROM 1-10577579-2 WHERE Institution = 'Tougaloo College'"}
{"text": "table: 1-10592536-8\ncolumns: Team, Outgoing manager, Manner of departure, Date of vacancy, Replaced by, Date of appointment, Position in table\nQ: What is the date of vacancy when the date of appointment is 28 november 2007 and replaced by is alex mcleish?\nA: SELECT Date of vacancy FROM 1-10592536-8 WHERE Date of appointment = '28 November 2007' AND Replaced by = 'Alex McLeish'"}
{"text": "table: 1-10592536-8\ncolumns: Team, Outgoing manager, Manner of departure, Date of vacancy, Replaced by, Date of appointment, Position in table\nQ: What is the date of appointment when the date of vacancy is 21 december 2007?\nA: SELECT Date of appointment FROM 1-10592536-8 WHERE Date of vacancy = '21 December 2007'"}
{"text": "table: 1-10592536-8\ncolumns: Team, Outgoing manager, Manner of departure, Date of vacancy, Replaced by, Date of appointment, Position in table\nQ: Who replaced when team is wigan athletic?\nA: SELECT Replaced by FROM 1-10592536-8 WHERE Team = 'Wigan Athletic'"}
{"text": "table: 1-10592536-8\ncolumns: Team, Outgoing manager, Manner of departure, Date of vacancy, Replaced by, Date of appointment, Position in table\nQ: What is the date of vacancy when the team is manchester city and replaced by is mark hughes?\nA: SELECT Date of vacancy FROM 1-10592536-8 WHERE Team = 'Manchester City' AND Replaced by = 'Mark Hughes'"}
{"text": "table: 1-10592536-8\ncolumns: Team, Outgoing manager, Manner of departure, Date of vacancy, Replaced by, Date of appointment, Position in table\nQ: What is the date of appointment when replaced by is roy hodgson?\nA: SELECT Date of appointment FROM 1-10592536-8 WHERE Replaced by = 'Roy Hodgson'"}
{"text": "table: 1-10592536-8\ncolumns: Team, Outgoing manager, Manner of departure, Date of vacancy, Replaced by, Date of appointment, Position in table\nQ: Who replaced when position in table is pre-season?\nA: SELECT Replaced by FROM 1-10592536-8 WHERE Position in table = 'Pre-season'"}
{"text": "table: 1-1059743-1\ncolumns: Rank, Member Association, Points, Group stage, Play-off, AFC Cup\nQ: How many games had a score value of 813.5 in post-season play?\nA: SELECT COUNT Play-off FROM 1-1059743-1 WHERE Points = '813.5'"}
{"text": "table: 1-1059743-1\ncolumns: Rank, Member Association, Points, Group stage, Play-off, AFC Cup\nQ: Did any team score games that totaled up to 860.5?\nA: SELECT Play-off FROM 1-1059743-1 WHERE Points = '860.5'"}
{"text": "table: 1-10595672-1\ncolumns: Date, Opponent, Home / Away, Score, High points, High rebounds, High assists, Location/Attendance, Record\nQ: What was the score of the game when the team reached a record of 6-9?\nA: SELECT Score FROM 1-10595672-1 WHERE Record = '6-9'"}
{"text": "table: 1-10581768-2\ncolumns: Institution, Nickname, Location, Founded, Type, Enrollment\nQ: What type institution is point park university\nA: SELECT Type FROM 1-10581768-2 WHERE Institution = 'Point Park University'"}
{"text": "table: 1-10581768-2\ncolumns: Institution, Nickname, Location, Founded, Type, Enrollment\nQ: How many institutions are located in wilmore, kentucky and private\nA: SELECT MAX Founded FROM 1-10581768-2 WHERE Type = 'Private' AND Location = 'Wilmore, Kentucky'"}
{"text": "table: 1-10581768-2\ncolumns: Institution, Nickname, Location, Founded, Type, Enrollment\nQ: point park university is what type of institution\nA: SELECT Type FROM 1-10581768-2 WHERE Institution = 'Point Park University'"}
{"text": "table: 1-10581768-2\ncolumns: Institution, Nickname, Location, Founded, Type, Enrollment\nQ: how many founded dates are listed for carlow university 1\nA: SELECT COUNT Founded FROM 1-10581768-2 WHERE Institution = 'Carlow University 1'"}
{"text": "table: 1-10610087-6\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date\nQ: Who wrote the episode titled \"black\"?\nA: SELECT Written by FROM 1-10610087-6 WHERE Title = '\"Black\"'"}
{"text": "table: 1-10610087-6\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date\nQ: Who are the writers for the episode \"solo\"?\nA: SELECT Written by FROM 1-10610087-6 WHERE Title = '\"Solo\"'"}
{"text": "table: 1-10610087-3\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date\nQ: what is the original air date of the episode no in season 9?\nA: SELECT Original air date FROM 1-10610087-3 WHERE No. in season = 9"}
{"text": "table: 1-10610087-3\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date\nQ: What is the title of the episode written by denis leary, peter tolan and evan reilly?\nA: SELECT Title FROM 1-10610087-3 WHERE Written by = 'Denis Leary, Peter Tolan and Evan Reilly'"}
{"text": "table: 1-10610087-3\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date\nQ: How many episodes were titles \"voicemail\"?\nA: SELECT COUNT Directed by FROM 1-10610087-3 WHERE Title = '\"Voicemail\"'"}
{"text": "table: 1-10602294-1\ncolumns: Name, Dates active, Peak classification, Windspeeds, Pressure, Areas affected, Damage (USD), Deaths, Refs\nQ: When was Kamba active?\nA: SELECT Dates active FROM 1-10602294-1 WHERE Name = 'Kamba'"}
{"text": "table: 1-10602294-1\ncolumns: Name, Dates active, Peak classification, Windspeeds, Pressure, Areas affected, Damage (USD), Deaths, Refs\nQ: What was the cyclone's pressure in the storm that death was equal to 95km/h (60mph)?\nA: SELECT Pressure FROM 1-10602294-1 WHERE Deaths = '95km/h (60mph)'"}
{"text": "table: 1-10602294-1\ncolumns: Name, Dates active, Peak classification, Windspeeds, Pressure, Areas affected, Damage (USD), Deaths, Refs\nQ: What were the active dates for the storm that had 185km/h (115mph) deaths?\nA: SELECT Dates active FROM 1-10602294-1 WHERE Deaths = '185km/h (115mph)'"}
{"text": "table: 1-10602294-1\ncolumns: Name, Dates active, Peak classification, Windspeeds, Pressure, Areas affected, Damage (USD), Deaths, Refs\nQ: What was the damage (usd) from the cyclones that measured 1003hPa (29.62inHg) pressure?\nA: SELECT Damage (USD) FROM 1-10602294-1 WHERE Pressure = '1003hPa (29.62inHg)'"}
{"text": "table: 1-10621256-1\ncolumns: Player, Matches, Inns, N/O, Runs, High Score, Average, 100, 50, Catches, Stump\nQ:  what's the\u00a0average\u00a0where\u00a0high score\u00a0is 120\nA: SELECT Average FROM 1-10621256-1 WHERE High Score = 120"}
{"text": "table: 1-10621256-1\ncolumns: Player, Matches, Inns, N/O, Runs, High Score, Average, 100, 50, Catches, Stump\nQ:  what's the\u00a0player\u00a0where\u00a050\u00a0is 2 and\u00a0n/o\u00a0is 0\nA: SELECT Player FROM 1-10621256-1 WHERE 50 = 2 AND N/O = 0"}
{"text": "table: 1-10621256-1\ncolumns: Player, Matches, Inns, N/O, Runs, High Score, Average, 100, 50, Catches, Stump\nQ:  what's the\u00a0player\u00a0where\u00a0inns\u00a0is 21\nA: SELECT Player FROM 1-10621256-1 WHERE Inns = 21"}
{"text": "table: 1-106367-2\ncolumns: General election, # of candidates, # of seats won, % of popular vote, Result\nQ: Which general election had a pq majority and a 44.75% of the popular vote?\nA: SELECT General election FROM 1-106367-2 WHERE Result = 'PQ majority' AND % of popular vote = '44.75%'"}
{"text": "table: 1-106367-2\ncolumns: General election, # of candidates, # of seats won, % of popular vote, Result\nQ: What is the least number of candidates running were there when 80 seats were won?\nA: SELECT MIN # of candidates FROM 1-106367-2 WHERE # of seats won = 80"}
{"text": "table: 1-106367-2\ncolumns: General election, # of candidates, # of seats won, % of popular vote, Result\nQ: How many seats were won in the election with 125 candidates?\nA: SELECT COUNT # of seats won FROM 1-106367-2 WHERE # of candidates = 125"}
{"text": "table: 1-10647639-1\ncolumns: Week, Date, Opponent, Result, Game site, Record, Attendance\nQ: How many weeks are there?\nA: SELECT MAX Week FROM 1-10647639-1"}
{"text": "table: 1-10647639-1\ncolumns: Week, Date, Opponent, Result, Game site, Record, Attendance\nQ: How many people attended the game against the indianapolis colts?\nA: SELECT COUNT Attendance FROM 1-10647639-1 WHERE Opponent = 'Indianapolis Colts'"}
{"text": "table: 1-10647639-1\ncolumns: Week, Date, Opponent, Result, Game site, Record, Attendance\nQ: On december 16, 1985, all the records were what?\nA: SELECT Record FROM 1-10647639-1 WHERE Date = 'December 16, 1985'"}
{"text": "table: 1-10646790-2\ncolumns: Week, Date, Opponent, Result, Stadium, Record, Attendance\nQ: How many results are there for the 0-4 record?\nA: SELECT COUNT Result FROM 1-10646790-2 WHERE Record = '0-4'"}
{"text": "table: 1-10646790-2\ncolumns: Week, Date, Opponent, Result, Stadium, Record, Attendance\nQ: How many weeks are there that include the date October 11, 1969.\nA: SELECT COUNT Week FROM 1-10646790-2 WHERE Date = 'October 11, 1969'"}
{"text": "table: 1-10646790-2\ncolumns: Week, Date, Opponent, Result, Stadium, Record, Attendance\nQ: How many weeks are there that include the date November 9, 1969.\nA: SELECT COUNT Week FROM 1-10646790-2 WHERE Date = 'November 9, 1969'"}
{"text": "table: 1-10646790-2\ncolumns: Week, Date, Opponent, Result, Stadium, Record, Attendance\nQ: How many records are there at the War Memorial Stadium?\nA: SELECT COUNT Record FROM 1-10646790-2 WHERE Stadium = 'War Memorial Stadium'"}
{"text": "table: 1-10646790-2\ncolumns: Week, Date, Opponent, Result, Stadium, Record, Attendance\nQ: What was the minimum attendance on December 7, 1969?\nA: SELECT MIN Attendance FROM 1-10646790-2 WHERE Date = 'December 7, 1969'"}
{"text": "table: 1-10647401-1\ncolumns: Week, Opponent, Result, Stadium, Record, Attendance\nQ: What week corresponds to the last one to be played at the memorial stadium?\nA: SELECT MAX Week FROM 1-10647401-1 WHERE Stadium = 'Memorial Stadium'"}
{"text": "table: 1-10647401-1\ncolumns: Week, Opponent, Result, Stadium, Record, Attendance\nQ: In which stadium is the week 5 game played?\nA: SELECT Stadium FROM 1-10647401-1 WHERE Week = 5"}
{"text": "table: 1-10664957-2\ncolumns: 1st players choice, 2nd players choice, Probability 1st player wins, Probability 2nd player wins, Probability of a draw\nQ: In Penney's game what is the probability where the 1st player wins if the probability of a draw is 8.28% and the 2nd player chooses B BR?\nA: SELECT Probability 1st player wins FROM 1-10664957-2 WHERE Probability of a draw = '8.28%' AND 2nd players choice = 'B BR'"}
{"text": "table: 1-10664957-2\ncolumns: 1st players choice, 2nd players choice, Probability 1st player wins, Probability 2nd player wins, Probability of a draw\nQ: If the first player chooses RB B, what is the second player's choices?\nA: SELECT 2nd players choice FROM 1-10664957-2 WHERE 1st players choice = 'RB B'"}
{"text": "table: 1-10664957-2\ncolumns: 1st players choice, 2nd players choice, Probability 1st player wins, Probability 2nd player wins, Probability of a draw\nQ: What is the probability where the second player wins where their choice is R RB and the first player has a 5.18% chance of winning?\nA: SELECT Probability 2nd player wins FROM 1-10664957-2 WHERE 2nd players choice = 'R RB' AND Probability 1st player wins = '5.18%'"}
{"text": "table: 1-10664957-2\ncolumns: 1st players choice, 2nd players choice, Probability 1st player wins, Probability 2nd player wins, Probability of a draw\nQ: What are the chances the first player will win if the 2nd player has an 80.11% chance of winning with the choice of R RB?\nA: SELECT Probability 1st player wins FROM 1-10664957-2 WHERE Probability 2nd player wins = '80.11%' AND 2nd players choice = 'R RB'"}
{"text": "table: 1-10664957-2\ncolumns: 1st players choice, 2nd players choice, Probability 1st player wins, Probability 2nd player wins, Probability of a draw\nQ: What are the chances that player 2 wins if player 1's choice is BB R?\nA: SELECT Probability 2nd player wins FROM 1-10664957-2 WHERE 1st players choice = 'BB R'"}
{"text": "table: 1-10664957-2\ncolumns: 1st players choice, 2nd players choice, Probability 1st player wins, Probability 2nd player wins, Probability of a draw\nQ: How high is the chance that player 1 wins if player 2 has an 88.29% chance of winning with the choice of R RB?\nA: SELECT Probability 1st player wins FROM 1-10664957-2 WHERE Probability 2nd player wins = '88.29%' AND 2nd players choice = 'R RB'"}
{"text": "table: 1-10650711-1\ncolumns: Pick #, NFL Team, Player, Position, College\nQ:  what is the\u00a0nfl team\u00a0where\u00a0player\u00a0is thane gash\nA: SELECT NFL Team FROM 1-10650711-1 WHERE Player = 'Thane Gash'"}
{"text": "table: 1-10650711-1\ncolumns: Pick #, NFL Team, Player, Position, College\nQ: what is the maximum\u00a0pick #\u00a0where\u00a0player\u00a0is anthony blaylock\nA: SELECT MAX Pick # FROM 1-10650711-1 WHERE Player = 'Anthony Blaylock'"}
{"text": "table: 1-10650711-1\ncolumns: Pick #, NFL Team, Player, Position, College\nQ:  what's the\u00a0nfl team\u00a0where\u00a0player\u00a0is clifford charlton\nA: SELECT NFL Team FROM 1-10650711-1 WHERE Player = 'Clifford Charlton'"}
{"text": "table: 1-10650711-1\ncolumns: Pick #, NFL Team, Player, Position, College\nQ:  what's the\u00a0position\u00a0where\u00a0player\u00a0is anthony blaylock\nA: SELECT Position FROM 1-10650711-1 WHERE Player = 'Anthony Blaylock'"}
{"text": "table: 1-10650711-1\ncolumns: Pick #, NFL Team, Player, Position, College\nQ: what is the minimum\u00a0pick #\u00a0where\u00a0position\u00a0is defensive tackle\nA: SELECT MIN Pick # FROM 1-10650711-1 WHERE Position = 'Defensive Tackle'"}
{"text": "table: 1-1067441-1\ncolumns: Province, Population (2004 estimate), Area (km\u00b2), Density, GDP (2003, PPS in mil. \u20ac ), GDP per cap. (2003, in \u20ac)\nQ: Which province has a density of 971.4?\nA: SELECT Province FROM 1-1067441-1 WHERE Density = '971.4'"}
{"text": "table: 1-1067441-1\ncolumns: Province, Population (2004 estimate), Area (km\u00b2), Density, GDP (2003, PPS in mil. \u20ac ), GDP per cap. (2003, in \u20ac)\nQ: What is Friesland's gdp per capita?\nA: SELECT MIN GDP per cap. (2003, in \u20ac) FROM 1-1067441-1 WHERE Province = 'Friesland'"}
{"text": "table: 1-1067441-1\ncolumns: Province, Population (2004 estimate), Area (km\u00b2), Density, GDP (2003, PPS in mil. \u20ac ), GDP per cap. (2003, in \u20ac)\nQ: What is the area of the place that has a population density of 331.4?\nA: SELECT MAX Area (km\u00b2) FROM 1-1067441-1 WHERE Density = '331.4'"}
{"text": "table: 1-1067441-1\ncolumns: Province, Population (2004 estimate), Area (km\u00b2), Density, GDP (2003, PPS in mil. \u20ac ), GDP per cap. (2003, in \u20ac)\nQ: Which province has a gdp of 38355\u20ac  million euros?\nA: SELECT Province FROM 1-1067441-1 WHERE GDP (2003, PPS in mil. \u20ac ) = 38355"}
{"text": "table: 1-1067441-1\ncolumns: Province, Population (2004 estimate), Area (km\u00b2), Density, GDP (2003, PPS in mil. \u20ac ), GDP per cap. (2003, in \u20ac)\nQ: What is the population estimate for the place that gad a 18496\u20ac  million euro gdp?\nA: SELECT Population (2004 estimate) FROM 1-1067441-1 WHERE GDP (2003, PPS in mil. \u20ac ) = 18496"}
{"text": "table: 1-10701133-1\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Featured character(s), Original air date, U.S. viewers (million)\nQ: What is the title when original air date is may15,2008?\nA: SELECT Title FROM 1-10701133-1 WHERE Original air date = 'May15,2008'"}
{"text": "table: 1-10701133-1\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Featured character(s), Original air date, U.S. viewers (million)\nQ: What is the highest no. in season?\nA: SELECT MAX No. in season FROM 1-10701133-1"}
{"text": "table: 1-10701133-1\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Featured character(s), Original air date, U.S. viewers (million)\nQ: Who directed the episode where u.s. viewers (million) is 12.90?\nA: SELECT Directed by FROM 1-10701133-1 WHERE U.S. viewers (million) = '12.90'"}
{"text": "table: 1-1067134-1\ncolumns: DVD Name, # of Ep, Region 1, Region 2, Region 4\nQ: How many episodes aired in Region 2 beginning May 26, 2008?\nA: SELECT MIN # of Ep FROM 1-1067134-1 WHERE Region 2 = 'May 26, 2008'"}
{"text": "table: 1-1067134-1\ncolumns: DVD Name, # of Ep, Region 1, Region 2, Region 4\nQ: What date did the DVD for season six come out in region 2?\nA: SELECT Region 2 FROM 1-1067134-1 WHERE DVD Name = 'Season Six'"}
{"text": "table: 1-1067134-1\ncolumns: DVD Name, # of Ep, Region 1, Region 2, Region 4\nQ: What is the least amount of season epidsodes?\nA: SELECT MIN # of Ep FROM 1-1067134-1"}
{"text": "table: 1-1067134-1\ncolumns: DVD Name, # of Ep, Region 1, Region 2, Region 4\nQ: What DVD season/name for region 2 was released August 22, 2010?\nA: SELECT DVD Name FROM 1-1067134-1 WHERE Region 2 = 'August 22, 2010'"}
{"text": "table: 1-10705060-1\ncolumns: Season, Series, Team Name, Races, Poles, Wins, Points, Position\nQ: How many points for 2005?\nA: SELECT COUNT Points FROM 1-10705060-1 WHERE Season = '2005'"}
{"text": "table: 1-10705060-1\ncolumns: Season, Series, Team Name, Races, Poles, Wins, Points, Position\nQ: what is the score for the dams?\nA: SELECT Points FROM 1-10705060-1 WHERE Team Name = 'DAMS'"}
{"text": "table: 1-10705060-1\ncolumns: Season, Series, Team Name, Races, Poles, Wins, Points, Position\nQ: how many positions in 2009?\nA: SELECT COUNT Position FROM 1-10705060-1 WHERE Season = '2009'"}
{"text": "table: 1-10705060-1\ncolumns: Season, Series, Team Name, Races, Poles, Wins, Points, Position\nQ: what is the least number of poles?\nA: SELECT MIN Poles FROM 1-10705060-1"}
{"text": "table: 1-10705060-1\ncolumns: Season, Series, Team Name, Races, Poles, Wins, Points, Position\nQ: Which series with 62 points?\nA: SELECT Series FROM 1-10705060-1 WHERE Points = 62"}
{"text": "table: 1-10705060-1\ncolumns: Season, Series, Team Name, Races, Poles, Wins, Points, Position\nQ: What is the total for 10th position?\nA: SELECT COUNT Points FROM 1-10705060-1 WHERE Position = '10th'"}
{"text": "table: 1-10707142-2\ncolumns: Rnd, Race Name, Circuit, City/Location, Date, Pole position, Winning driver, Winning team, Report\nQ: how many reports of races took place on october 16?\nA: SELECT COUNT Report FROM 1-10707142-2 WHERE Date = 'October 16'"}
{"text": "table: 1-10707142-2\ncolumns: Rnd, Race Name, Circuit, City/Location, Date, Pole position, Winning driver, Winning team, Report\nQ: what is the name of the report that lists the race name as long beach grand prix?\nA: SELECT Report FROM 1-10707142-2 WHERE Race Name = 'Long Beach Grand Prix'"}
{"text": "table: 1-10707142-2\ncolumns: Rnd, Race Name, Circuit, City/Location, Date, Pole position, Winning driver, Winning team, Report\nQ: what is the report called where the circuit took place at the nazareth speedway?\nA: SELECT Report FROM 1-10707142-2 WHERE Circuit = 'Nazareth Speedway'"}
{"text": "table: 1-10707142-2\ncolumns: Rnd, Race Name, Circuit, City/Location, Date, Pole position, Winning driver, Winning team, Report\nQ: what is the name of the race where newman/haas racing is the winning team and rick mears is at the pole position?\nA: SELECT Race Name FROM 1-10707142-2 WHERE Winning team = 'Newman/Haas Racing' AND Pole position = 'Rick Mears'"}
{"text": "table: 1-10707142-2\ncolumns: Rnd, Race Name, Circuit, City/Location, Date, Pole position, Winning driver, Winning team, Report\nQ: meadowlands sports complex is the circuit at which city/location?\nA: SELECT City/Location FROM 1-10707142-2 WHERE Circuit = 'Meadowlands Sports Complex'"}
{"text": "table: 1-10710364-2\ncolumns: Religious group, Population %, Growth (1991\u20132001), Sex ratio (total), Literacy (%), Work participation (%), Sex ratio (rural), Sex ratio (urban), Sex ratio (child)\nQ: What is the literacy rate for groups that grew 103.1% between 1991 and 2001?\nA: SELECT Literacy (%) FROM 1-10710364-2 WHERE Growth (1991\u20132001) = '103.1%'"}
{"text": "table: 1-10710364-2\ncolumns: Religious group, Population %, Growth (1991\u20132001), Sex ratio (total), Literacy (%), Work participation (%), Sex ratio (rural), Sex ratio (urban), Sex ratio (child)\nQ: What is the lowest sex ratio in rural areas?\nA: SELECT MIN Sex ratio (rural) FROM 1-10710364-2"}
{"text": "table: 1-10710364-2\ncolumns: Religious group, Population %, Growth (1991\u20132001), Sex ratio (total), Literacy (%), Work participation (%), Sex ratio (rural), Sex ratio (urban), Sex ratio (child)\nQ: What is the lowest child sex ratio in groups where employment is 31.3%?\nA: SELECT MIN Sex ratio (child) FROM 1-10710364-2 WHERE Work participation (%) = '31.3%'"}
{"text": "table: 1-10710364-2\ncolumns: Religious group, Population %, Growth (1991\u20132001), Sex ratio (total), Literacy (%), Work participation (%), Sex ratio (rural), Sex ratio (urban), Sex ratio (child)\nQ: What is the population percentage of the group where the rural sex ratio is 953?\nA: SELECT Population % FROM 1-10710364-2 WHERE Sex ratio (rural) = 953"}
{"text": "table: 1-10715317-2\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: What is the original air date for the title \"felonious monk\"?\nA: SELECT Original air date FROM 1-10715317-2 WHERE Title = '\"Felonious Monk\"'"}
{"text": "table: 1-10715317-2\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: What is the title of the episode directed by Peter Markle and written by Jerry Stahl?\nA: SELECT Title FROM 1-10715317-2 WHERE Directed by = 'Peter Markle' AND Written by = 'Jerry Stahl'"}
{"text": "table: 1-10715317-2\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: How many episodes were titled \"identity crisis\"?\nA: SELECT COUNT Directed by FROM 1-10715317-2 WHERE Title = '\"Identity Crisis\"'"}
{"text": "table: 1-10716893-3\ncolumns: Year, Network, Host, Pre-race analyst, Lap-by-lap, Color commentator(s), Pit reporters\nQ: Who does the lap-by-lap in 2011?\nA: SELECT Lap-by-lap FROM 1-10716893-3 WHERE Year = 2011"}
{"text": "table: 1-10716893-3\ncolumns: Year, Network, Host, Pre-race analyst, Lap-by-lap, Color commentator(s), Pit reporters\nQ: Which network has Marty Reid as host and lap-by-lap broadcaster?\nA: SELECT Network FROM 1-10716893-3 WHERE Lap-by-lap = 'Marty Reid' AND Host = 'Marty Reid'"}
{"text": "table: 1-10716893-3\ncolumns: Year, Network, Host, Pre-race analyst, Lap-by-lap, Color commentator(s), Pit reporters\nQ: How many pre-race analysis occur when Allen Bestwick does the lap-by-lap?\nA: SELECT COUNT Pre-race analyst FROM 1-10716893-3 WHERE Lap-by-lap = 'Allen Bestwick'"}
{"text": "table: 1-10718192-2\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: What's the highest season number of an episode in the series?\nA: SELECT MAX No. in season FROM 1-10718192-2"}
{"text": "table: 1-10718192-2\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: When did the episode titled \"Lucky Strike\" air for the first time?\nA: SELECT Original air date FROM 1-10718192-2 WHERE Title = '\"Lucky Strike\"'"}
{"text": "table: 1-10718192-2\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: Who was the writer of the episode titled \"One Hit Wonder\"?\nA: SELECT Written by FROM 1-10718192-2 WHERE Title = '\"One Hit Wonder\"'"}
{"text": "table: 1-10718192-2\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: What's the date of the first airing of the episode with series number 63?\nA: SELECT Original air date FROM 1-10718192-2 WHERE No. in series = 63"}
{"text": "table: 1-10718525-2\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: How many titles got a viewership of 26.53 million?\nA: SELECT COUNT Title FROM 1-10718525-2 WHERE U.S. viewers (millions) = '26.53'"}
{"text": "table: 1-10718525-2\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: How many viewers tuned into the show directed by Matt Earl Beesley?\nA: SELECT U.S. viewers (millions) FROM 1-10718525-2 WHERE Directed by = 'Matt Earl Beesley'"}
{"text": "table: 1-10718631-2\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: Who wrote episode 94?\nA: SELECT Written by FROM 1-10718631-2 WHERE No. in series = 94"}
{"text": "table: 1-10718631-2\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: Which episode was the number in the season where the number in the season is 10?\nA: SELECT No. in series FROM 1-10718631-2 WHERE No. in season = 10"}
{"text": "table: 1-10718631-2\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: How many episodes were in the season that had the epiosde of \"crow's feet\"?\nA: SELECT No. in season FROM 1-10718631-2 WHERE Title = '\"Crow's Feet\"'"}
{"text": "table: 1-10718631-2\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: When did the 113 episode air?\nA: SELECT Original air date FROM 1-10718631-2 WHERE No. in series = 113"}
{"text": "table: 1-10718631-2\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: How many titles were there for the 113 episode?\nA: SELECT COUNT Title FROM 1-10718631-2 WHERE No. in series = 113"}
{"text": "table: 1-10718984-2\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: What is the number in the season that Marlene Meyer wrote and 20.49 million people watched?\nA: SELECT MAX No. in season FROM 1-10718984-2 WHERE Written by = 'Marlene Meyer' AND U.S. viewers (millions) = '20.49'"}
{"text": "table: 1-10718984-2\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: When did the no. 23 show originally air?\nA: SELECT Original air date FROM 1-10718984-2 WHERE No. in season = 23"}
{"text": "table: 1-10725629-2\ncolumns: Rnd, Race Name, Circuit, City/Location, Date, Pole position, Fastest lap, Winning driver, Winning team, Report\nQ: Which circuits had a race on October 4?\nA: SELECT Circuit FROM 1-10725629-2 WHERE Date = 'October 4'"}
{"text": "table: 1-10725629-2\ncolumns: Rnd, Race Name, Circuit, City/Location, Date, Pole position, Fastest lap, Winning driver, Winning team, Report\nQ: In which reports does Michael Andretti have the pole position and Galles-Kraco Racing is the winning team?\nA: SELECT Report FROM 1-10725629-2 WHERE Pole position = 'Michael Andretti' AND Winning team = 'Galles-Kraco Racing'"}
{"text": "table: 1-10725629-2\ncolumns: Rnd, Race Name, Circuit, City/Location, Date, Pole position, Fastest lap, Winning driver, Winning team, Report\nQ: How many rounds were there of the Bosch Spark Plug Grand Prix?\nA: SELECT COUNT Rnd FROM 1-10725629-2 WHERE Race Name = 'Bosch Spark Plug Grand Prix'"}
{"text": "table: 1-10725629-2\ncolumns: Rnd, Race Name, Circuit, City/Location, Date, Pole position, Fastest lap, Winning driver, Winning team, Report\nQ: Which rounds were held on August 9?\nA: SELECT Rnd FROM 1-10725629-2 WHERE Date = 'August 9'"}
{"text": "table: 1-10725629-2\ncolumns: Rnd, Race Name, Circuit, City/Location, Date, Pole position, Fastest lap, Winning driver, Winning team, Report\nQ: On how many dates did the Michigan International Speedway hold a round?\nA: SELECT COUNT Date FROM 1-10725629-2 WHERE Circuit = 'Michigan International Speedway'"}
{"text": "table: 1-10722506-6\ncolumns: Conference, # of Bids, Record, Win %, Round of 32, Sweet Sixteen, Elite Eight, Final Four, Championship Game\nQ: Name the total number of bids of the sun belt conference\nA: SELECT COUNT # of Bids FROM 1-10722506-6 WHERE Conference = 'Sun Belt'"}
{"text": "table: 1-10722506-6\ncolumns: Conference, # of Bids, Record, Win %, Round of 32, Sweet Sixteen, Elite Eight, Final Four, Championship Game\nQ: Name the round of 32 in conference usa\nA: SELECT Round of 32 FROM 1-10722506-6 WHERE Conference = 'Conference USA'"}
{"text": "table: 1-10722506-6\ncolumns: Conference, # of Bids, Record, Win %, Round of 32, Sweet Sixteen, Elite Eight, Final Four, Championship Game\nQ: What is the record when round of 32 is 0 and metro atlantic conference?\nA: SELECT Record FROM 1-10722506-6 WHERE Round of 32 = 0 AND Conference = 'Metro Atlantic'"}
{"text": "table: 1-10722506-6\ncolumns: Conference, # of Bids, Record, Win %, Round of 32, Sweet Sixteen, Elite Eight, Final Four, Championship Game\nQ: What is the number of bids with elite eight larger than 1.0\nA: SELECT COUNT # of Bids FROM 1-10722506-6 WHERE Elite Eight > 1.0"}
{"text": "table: 1-10749143-2\ncolumns: Series #, Season #, Title, Directed by, Written by, Original air date, Production code, U.S. viewers (millions)\nQ: Who directed the episode with production code 7aff03?\nA: SELECT Directed by FROM 1-10749143-2 WHERE Production code = '7AFF03'"}
{"text": "table: 1-10749143-2\ncolumns: Series #, Season #, Title, Directed by, Written by, Original air date, Production code, U.S. viewers (millions)\nQ: What is the title of the episode wtih 10.34 million U.S viewers?\nA: SELECT Title FROM 1-10749143-2 WHERE U.S. viewers (millions) = '10.34'"}
{"text": "table: 1-10748727-1\ncolumns: Season, Series, Team Name, Races, Wins, Poles, F/Laps, Podiums, Points, Position\nQ: What place is the team that completed 6 races?\nA: SELECT Position FROM 1-10748727-1 WHERE Races = 6"}
{"text": "table: 1-10748727-1\ncolumns: Season, Series, Team Name, Races, Wins, Poles, F/Laps, Podiums, Points, Position\nQ: how much did the british formula three called \"fortec motorsport\" score?\nA: SELECT Points FROM 1-10748727-1 WHERE Series = 'British Formula Three' AND Team Name = 'Fortec Motorsport'"}
{"text": "table: 1-10748727-1\ncolumns: Season, Series, Team Name, Races, Wins, Poles, F/Laps, Podiums, Points, Position\nQ: how many races were in 2009 with 0 wins?\nA: SELECT Races FROM 1-10748727-1 WHERE Season = '2009' AND Wins = 0"}
{"text": "table: 1-10748727-1\ncolumns: Season, Series, Team Name, Races, Wins, Poles, F/Laps, Podiums, Points, Position\nQ: What years did art grand prix compete?\nA: SELECT Season FROM 1-10748727-1 WHERE Team Name = 'ART Grand Prix'"}
{"text": "table: 1-10748727-1\ncolumns: Season, Series, Team Name, Races, Wins, Poles, F/Laps, Podiums, Points, Position\nQ: What year had a score of 9?\nA: SELECT Season FROM 1-10748727-1 WHERE Points = '9'"}
{"text": "table: 1-10748727-1\ncolumns: Season, Series, Team Name, Races, Wins, Poles, F/Laps, Podiums, Points, Position\nQ: what is the greatest number of wins by japanese formula three?\nA: SELECT MAX Wins FROM 1-10748727-1 WHERE Series = 'Japanese Formula Three'"}
{"text": "table: 1-10749367-3\ncolumns: #, Air Date, Challenge, Winner, Test-taker, Passed?\nQ: Who took test #4?\nA: SELECT Test-taker FROM 1-10749367-3 WHERE # = 4"}
{"text": "table: 1-10749367-3\ncolumns: #, Air Date, Challenge, Winner, Test-taker, Passed?\nQ: What episode aired on 18 April 2007?\nA: SELECT MIN # FROM 1-10749367-3 WHERE Air Date = '18 April 2007'"}
{"text": "table: 1-10749367-3\ncolumns: #, Air Date, Challenge, Winner, Test-taker, Passed?\nQ: Who had the challenge of night driving?\nA: SELECT Test-taker FROM 1-10749367-3 WHERE Challenge = 'Night Driving'"}
{"text": "table: 1-10798928-1\ncolumns: Year (Ceremony), Film title used in nomination, Original title, Director, Result\nQ: How many directors were there for the film Course Completed?\nA: SELECT COUNT Director FROM 1-10798928-1 WHERE Film title used in nomination = 'Course Completed'"}
{"text": "table: 1-10798928-1\ncolumns: Year (Ceremony), Film title used in nomination, Original title, Director, Result\nQ: Who directed El Nido?\nA: SELECT Director FROM 1-10798928-1 WHERE Original title = 'El nido'"}
{"text": "table: 1-10798928-1\ncolumns: Year (Ceremony), Film title used in nomination, Original title, Director, Result\nQ: Who directed Dulcinea?\nA: SELECT Director FROM 1-10798928-1 WHERE Original title = 'Dulcinea'"}
{"text": "table: 1-10797463-1\ncolumns: Village (German), Village (Slovenian), Number of people 1991, Percent of Slovenes 1991, Percent of Slovenes 1951\nQ: What are the slovenian names of the villages that had 65.9% of slovenes in 1951?\nA: SELECT Village (Slovenian) FROM 1-10797463-1 WHERE Percent of Slovenes 1951 = '65.9%'"}
{"text": "table: 1-10797463-1\ncolumns: Village (German), Village (Slovenian), Number of people 1991, Percent of Slovenes 1991, Percent of Slovenes 1951\nQ: What are the slovenian names of the villages that had 16.7% of slovenes in 1991?\nA: SELECT Village (Slovenian) FROM 1-10797463-1 WHERE Percent of Slovenes 1991 = '16.7%'"}
{"text": "table: 1-10797463-1\ncolumns: Village (German), Village (Slovenian), Number of people 1991, Percent of Slovenes 1991, Percent of Slovenes 1951\nQ: How many villages had 21.7% of slovenes in 1991?\nA: SELECT COUNT Village (German) FROM 1-10797463-1 WHERE Percent of Slovenes 1991 = '21.7%'"}
{"text": "table: 1-10797463-1\ncolumns: Village (German), Village (Slovenian), Number of people 1991, Percent of Slovenes 1991, Percent of Slovenes 1951\nQ: what percent of slovenes did the village called \u010dahor\u010de in slovenian have in 1991?\nA: SELECT Percent of Slovenes 1991 FROM 1-10797463-1 WHERE Village (Slovenian) = '\u010cahor\u010de'"}
{"text": "table: 1-10797463-1\ncolumns: Village (German), Village (Slovenian), Number of people 1991, Percent of Slovenes 1991, Percent of Slovenes 1951\nQ: What is the slovenian name for the village that in german is known as st.margarethen?\nA: SELECT Village (Slovenian) FROM 1-10797463-1 WHERE Village (German) = 'St.Margarethen'"}
{"text": "table: 1-10812293-4\ncolumns: Game, Date, Team, Score, High points, High rebounds, High assists, Location Attendance, Record\nQ: For games on December 20, how many points did the scoring leaders get?\nA: SELECT High points FROM 1-10812293-4 WHERE Date = 'December 20'"}
{"text": "table: 1-10812293-4\ncolumns: Game, Date, Team, Score, High points, High rebounds, High assists, Location Attendance, Record\nQ: Who was the scoring leader and how many points did he get in games on December 23?\nA: SELECT High points FROM 1-10812293-4 WHERE Date = 'December 23'"}
{"text": "table: 1-10812938-3\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What is the pick # for the position de?\nA: SELECT Pick # FROM 1-10812938-3 WHERE Position = 'DE'"}
{"text": "table: 1-10812938-3\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: Which player went to college at Saint Mary's?\nA: SELECT Player FROM 1-10812938-3 WHERE College = 'Saint Mary's'"}
{"text": "table: 1-10812938-3\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What is the position for the player with cfl team Winnipeg blue bombers?\nA: SELECT Position FROM 1-10812938-3 WHERE CFL Team = 'Winnipeg Blue Bombers'"}
{"text": "table: 1-10812938-3\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: Which player went to college at Laval?\nA: SELECT Player FROM 1-10812938-3 WHERE College = 'Laval'"}
{"text": "table: 1-10812938-3\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What was the college for the player with the cfl team of Edmonton Eskimos (via calgary)?\nA: SELECT College FROM 1-10812938-3 WHERE CFL Team = 'Edmonton Eskimos (via Calgary)'"}
{"text": "table: 1-10812938-5\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What's the team of the player from St. Francis Xavier College?\nA: SELECT CFL Team FROM 1-10812938-5 WHERE College = 'St. Francis Xavier'"}
{"text": "table: 1-10812938-5\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What player is on the Montreal Alouettes CFl team?\nA: SELECT Player FROM 1-10812938-5 WHERE CFL Team = 'Montreal Alouettes'"}
{"text": "table: 1-10812938-5\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What's the pick number of the player from Toronto Argonauts?\nA: SELECT MIN Pick # FROM 1-10812938-5 WHERE CFL Team = 'Toronto Argonauts'"}
{"text": "table: 1-10812938-5\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What's the pick number of the player whose position is CB?\nA: SELECT Pick # FROM 1-10812938-5 WHERE Position = 'CB'"}
{"text": "table: 1-10812938-5\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What's the pick number of the player from New Mexico?\nA: SELECT MAX Pick # FROM 1-10812938-5 WHERE College = 'New Mexico'"}
{"text": "table: 1-10812938-5\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What player went to Ohio State College?\nA: SELECT Player FROM 1-10812938-5 WHERE College = 'Ohio State'"}
{"text": "table: 1-1081459-1\ncolumns: Number Range, Builder, Introduced, No. Built, Region, Withdrawn\nQ: What is the minimum introduced value for the Departmental region?\nA: SELECT MIN Introduced FROM 1-1081459-1 WHERE Region = 'Departmental'"}
{"text": "table: 1-1081459-1\ncolumns: Number Range, Builder, Introduced, No. Built, Region, Withdrawn\nQ: What is the smallest introduced value?\nA: SELECT MIN Introduced FROM 1-1081459-1"}
{"text": "table: 1-10812938-4\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: Which CFL Teams drafted an OL in 2006?\nA: SELECT CFL Team FROM 1-10812938-4 WHERE Position = 'OL'"}
{"text": "table: 1-10812938-4\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: Which college is aligned to the Saskatchewan Roughriders?\nA: SELECT College FROM 1-10812938-4 WHERE CFL Team = 'Saskatchewan Roughriders'"}
{"text": "table: 1-10812938-4\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What Position did the Hamilton Tiger-Cats (via Ottawa) pick in the 2006 Draft.\nA: SELECT Position FROM 1-10812938-4 WHERE CFL Team = 'Hamilton Tiger-Cats (via Ottawa)'"}
{"text": "table: 1-10812938-4\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What is the earliest pick listed in the table.\nA: SELECT MIN Pick # FROM 1-10812938-4"}
{"text": "table: 1-10842344-1\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, Production code, U.S. viewers (millions)\nQ: What episode number had production code e4423?\nA: SELECT MAX No. in season FROM 1-10842344-1 WHERE Production code = 'E4423'"}
{"text": "table: 1-10842344-1\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, Production code, U.S. viewers (millions)\nQ: What's the latest episode in a season where the U.S. viewers totaled 14.37 million?\nA: SELECT MAX No. in season FROM 1-10842344-1 WHERE U.S. viewers (millions) = '14.37'"}
{"text": "table: 1-10842344-1\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, Production code, U.S. viewers (millions)\nQ: Who directed the episode \"Escape\"?\nA: SELECT Directed by FROM 1-10842344-1 WHERE Title = '\"Escape\"'"}
{"text": "table: 1-10819266-8\ncolumns: Season, Episodes, Time slot (EST), Season premiere, Season finale, TV season, Rank, Viewers (in millions)\nQ: How many seasons is the season finale on May 26, 2010?\nA: SELECT COUNT Season FROM 1-10819266-8 WHERE Season finale = 'May 26, 2010'"}
{"text": "table: 1-10819266-8\ncolumns: Season, Episodes, Time slot (EST), Season premiere, Season finale, TV season, Rank, Viewers (in millions)\nQ: What episodes are there where the season premier is September 23, 2009?\nA: SELECT Episodes FROM 1-10819266-8 WHERE Season premiere = 'September 23, 2009'"}
{"text": "table: 1-10819266-8\ncolumns: Season, Episodes, Time slot (EST), Season premiere, Season finale, TV season, Rank, Viewers (in millions)\nQ: What is the season finale for season 4?\nA: SELECT Season finale FROM 1-10819266-8 WHERE Season = 4"}
{"text": "table: 1-10819266-8\ncolumns: Season, Episodes, Time slot (EST), Season premiere, Season finale, TV season, Rank, Viewers (in millions)\nQ: How many season premiers have a rank of #21?\nA: SELECT Season premiere FROM 1-10819266-8 WHERE Rank = '#21'"}
{"text": "table: 1-10819266-8\ncolumns: Season, Episodes, Time slot (EST), Season premiere, Season finale, TV season, Rank, Viewers (in millions)\nQ: What are the seasons where September 26, 2007 is the season premier?\nA: SELECT TV season FROM 1-10819266-8 WHERE Season premiere = 'September 26, 2007'"}
{"text": "table: 1-10818465-1\ncolumns: Model, RU, Max processors, Processor frequency, Max memory, Max disk capacity, GA Date\nQ: What max processor has a maximum memory of 256 gb?\nA: SELECT Max processors FROM 1-10818465-1 WHERE Max memory = '256 GB'"}
{"text": "table: 1-10818465-1\ncolumns: Model, RU, Max processors, Processor frequency, Max memory, Max disk capacity, GA Date\nQ: What is the max memory of the t5120 model?\nA: SELECT Max memory FROM 1-10818465-1 WHERE Model = 'T5120'"}
{"text": "table: 1-10818465-1\ncolumns: Model, RU, Max processors, Processor frequency, Max memory, Max disk capacity, GA Date\nQ: What is the lowest ru?\nA: SELECT MIN RU FROM 1-10818465-1"}
{"text": "table: 1-10818465-1\ncolumns: Model, RU, Max processors, Processor frequency, Max memory, Max disk capacity, GA Date\nQ: What ga date do the models with 1.0, 1.2, 1.4ghz processor frequencies have?\nA: SELECT GA Date FROM 1-10818465-1 WHERE Processor frequency = '1.0, 1.2, 1.4GHz'"}
{"text": "table: 1-10818465-1\ncolumns: Model, RU, Max processors, Processor frequency, Max memory, Max disk capacity, GA Date\nQ: What is the ga date of the t5120 model?\nA: SELECT GA Date FROM 1-10818465-1 WHERE Model = 'T5120'"}
{"text": "table: 1-10815352-1\ncolumns: League, Sport, Country, Season, Games, Average attendance, Total attendance\nQ: What is the sport of the La Liga league?\nA: SELECT Sport FROM 1-10815352-1 WHERE League = 'La Liga'"}
{"text": "table: 1-10815352-1\ncolumns: League, Sport, Country, Season, Games, Average attendance, Total attendance\nQ: What's the minimum total attendance of the Premier League association football?\nA: SELECT MIN Total attendance FROM 1-10815352-1 WHERE Sport = 'Association football' AND League = 'Premier League'"}
{"text": "table: 1-10815352-1\ncolumns: League, Sport, Country, Season, Games, Average attendance, Total attendance\nQ: What's the average attendance of the leagues in the season of 2013?\nA: SELECT MIN Average attendance FROM 1-10815352-1 WHERE Season = '2013'"}
{"text": "table: 1-10815352-1\ncolumns: League, Sport, Country, Season, Games, Average attendance, Total attendance\nQ: What's the total attendance of the leagues in season of 2010?\nA: SELECT COUNT Total attendance FROM 1-10815352-1 WHERE Season = '2010'"}
{"text": "table: 1-10874596-1\ncolumns: Year [e ] (Ceremony), Film title used in nomination, Original title, Director, Result\nQ: Who were the directors of the film submitted with the title Young T\u00f6rless?\nA: SELECT Director FROM 1-10874596-1 WHERE Film title used in nomination = 'Young T\u00f6rless'"}
{"text": "table: 1-10874596-1\ncolumns: Year [e ] (Ceremony), Film title used in nomination, Original title, Director, Result\nQ: What was the original title of the film submitted with the title A Woman in Flames?\nA: SELECT Original title FROM 1-10874596-1 WHERE Film title used in nomination = 'A Woman in Flames'"}
{"text": "table: 1-10874596-1\ncolumns: Year [e ] (Ceremony), Film title used in nomination, Original title, Director, Result\nQ: In what years was a film submitted with the title The Enigma of Kaspar Hauser?\nA: SELECT Year [e ] (Ceremony) FROM 1-10874596-1 WHERE Film title used in nomination = 'The Enigma of Kaspar Hauser'"}
{"text": "table: 1-10874596-1\ncolumns: Year [e ] (Ceremony), Film title used in nomination, Original title, Director, Result\nQ: Who were the directors of the film with the original title o.k.?\nA: SELECT Director FROM 1-10874596-1 WHERE Original title = 'o.k.'"}
{"text": "table: 1-1087659-2\ncolumns: Year, Division, League, Reg. Season, Playoffs, Avg. Attendance\nQ: What is the division for the division semifinals playoffs?\nA: SELECT Division FROM 1-1087659-2 WHERE Playoffs = 'Division Semifinals'"}
{"text": "table: 1-10908676-7\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date\nQ: What is the number in series of \"say uncle\"?\nA: SELECT No. in series FROM 1-10908676-7 WHERE Title = '\"Say Uncle\"'"}
{"text": "table: 1-10908676-7\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date\nQ: What is the title written by David Mamet?\nA: SELECT Title FROM 1-10908676-7 WHERE Written by = 'David Mamet'"}
{"text": "table: 1-10942714-1\ncolumns: Rank, English title, Chinese title, Average, Peak, Premiere, Finale, HK viewers\nQ: What was the finale for \u6f6e\u7206\u5927\u72c0\nA: SELECT Finale FROM 1-10942714-1 WHERE Chinese title = '\u6f6e\u7206\u5927\u72c0'"}
{"text": "table: 1-10942714-1\ncolumns: Rank, English title, Chinese title, Average, Peak, Premiere, Finale, HK viewers\nQ: What was the finale for  \u6f6e\u7206\u5927\u72c0\nA: SELECT Finale FROM 1-10942714-1 WHERE Chinese title = '\u6f6e\u7206\u5927\u72c0'"}
{"text": "table: 1-10942714-1\ncolumns: Rank, English title, Chinese title, Average, Peak, Premiere, Finale, HK viewers\nQ: How many viewers were there for the premier with 34\nA: SELECT HK viewers FROM 1-10942714-1 WHERE Premiere = 34"}
{"text": "table: 1-10942714-1\ncolumns: Rank, English title, Chinese title, Average, Peak, Premiere, Finale, HK viewers\nQ: How many are listed under \u6f6e\u7206\u5927\u72c0\nA: SELECT COUNT Peak FROM 1-10942714-1 WHERE Chinese title = '\u6f6e\u7206\u5927\u72c0'"}
{"text": "table: 1-10953197-2\ncolumns: No. in series, No. in season, Title, Director, Writer(s), Original air date, Production code\nQ: Who was the director of the episode with a production code of 2393059?\nA: SELECT Director FROM 1-10953197-2 WHERE Production code = '2393059'"}
{"text": "table: 1-10953197-2\ncolumns: No. in series, No. in season, Title, Director, Writer(s), Original air date, Production code\nQ: How many people wrote \"Michael's Game\"?\nA: SELECT COUNT Writer(s) FROM 1-10953197-2 WHERE Title = '\"Michael's Game\"'"}
{"text": "table: 1-10953197-2\ncolumns: No. in series, No. in season, Title, Director, Writer(s), Original air date, Production code\nQ: When did the episode title \"Duet For One\" air?\nA: SELECT Original air date FROM 1-10953197-2 WHERE Title = '\"Duet for One\"'"}
{"text": "table: 1-10935548-1\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, Production code, U.S. viewers (millions)\nQ: Which episode had 16.38 million U.S. viewers?\nA: SELECT Title FROM 1-10935548-1 WHERE U.S. viewers (millions) = '16.38'"}
{"text": "table: 1-10935548-1\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, Production code, U.S. viewers (millions)\nQ: What is the production code of the episode written by Jos\u00e9 Molina that aired on October 12, 2004?\nA: SELECT Production code FROM 1-10935548-1 WHERE Written by = 'Jos\u00e9 Molina' AND Original air date = 'October 12, 2004'"}
{"text": "table: 1-10935548-1\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, Production code, U.S. viewers (millions)\nQ: What was the original air date of the episode \"Quarry\"?\nA: SELECT Original air date FROM 1-10935548-1 WHERE Title = '\"Quarry\"'"}
{"text": "table: 1-10935548-1\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, Production code, U.S. viewers (millions)\nQ: Which episode was directed by Jean de Segonzac?\nA: SELECT Title FROM 1-10935548-1 WHERE Directed by = 'Jean de Segonzac'"}
{"text": "table: 1-10953197-3\ncolumns: No. in series, No. in season, Title, Director, Writer(s), Original air date, Production code\nQ: what are the original air dates with a production code of 2394087\nA: SELECT Original air date FROM 1-10953197-3 WHERE Production code = '2394087'"}
{"text": "table: 1-10953197-3\ncolumns: No. in series, No. in season, Title, Director, Writer(s), Original air date, Production code\nQ: Who are the writers for the title \"boxing sydney\"\nA: SELECT Writer(s) FROM 1-10953197-3 WHERE Title = '\"Boxing Sydney\"'"}
{"text": "table: 1-10953197-3\ncolumns: No. in series, No. in season, Title, Director, Writer(s), Original air date, Production code\nQ: What are the production codes for the title \"all about brooke\"\nA: SELECT Production code FROM 1-10953197-3 WHERE Title = '\"All About Brooke\"'"}
{"text": "table: 1-10953197-3\ncolumns: No. in series, No. in season, Title, Director, Writer(s), Original air date, Production code\nQ: Who are the writer(s) for the production code 2394084\nA: SELECT Writer(s) FROM 1-10953197-3 WHERE Production code = '2394084'"}
{"text": "table: 1-10953197-4\ncolumns: No. in series, No. in season, Title, Director, Writer(s), Original air date, Production code\nQ: What's the total number of episodes with the production code 2395113A?\nA: SELECT COUNT Title FROM 1-10953197-4 WHERE Production code = '2395113A'"}
{"text": "table: 1-10953197-4\ncolumns: No. in series, No. in season, Title, Director, Writer(s), Original air date, Production code\nQ: What's the number of the episode called \"Melrose Unglued\"?\nA: SELECT MAX No. in series FROM 1-10953197-4 WHERE Title = '\"Melrose Unglued\"'"}
{"text": "table: 1-10953197-4\ncolumns: No. in series, No. in season, Title, Director, Writer(s), Original air date, Production code\nQ: Who's the writer for the episode with a production code 2395114?\nA: SELECT Writer(s) FROM 1-10953197-4 WHERE Production code = '2395114'"}
{"text": "table: 1-10953197-4\ncolumns: No. in series, No. in season, Title, Director, Writer(s), Original air date, Production code\nQ: Who directed the episode titled \"Full Metal Betsy\"?\nA: SELECT Director FROM 1-10953197-4 WHERE Title = '\"Full Metal Betsy\"'"}
{"text": "table: 1-10953197-4\ncolumns: No. in series, No. in season, Title, Director, Writer(s), Original air date, Production code\nQ: What's the number of the episode with production code 2395118?\nA: SELECT No. in season FROM 1-10953197-4 WHERE Production code = '2395118'"}
{"text": "table: 1-10953197-4\ncolumns: No. in series, No. in season, Title, Director, Writer(s), Original air date, Production code\nQ: Who was the writer for the episode with production code 2395096?\nA: SELECT Writer(s) FROM 1-10953197-4 WHERE Production code = '2395096'"}
{"text": "table: 1-10932739-2\ncolumns: Planet, Planet Type, Semimajor Axis ( AU ), Orbital Period, Radial velocity (m/s), Detectable by:\nQ: What generation of spectrograph is most likely to detect a planet with a radial velocity of 0.089 m/s?\nA: SELECT Detectable by: FROM 1-10932739-2 WHERE Radial velocity (m/s) = '0.089'"}
{"text": "table: 1-10932739-2\ncolumns: Planet, Planet Type, Semimajor Axis ( AU ), Orbital Period, Radial velocity (m/s), Detectable by:\nQ: How long is the orbital period for the planet that has a semimajor axis of 5.20 au?\nA: SELECT Orbital Period FROM 1-10932739-2 WHERE Semimajor Axis ( AU ) = '5.20'"}
{"text": "table: 1-10932739-2\ncolumns: Planet, Planet Type, Semimajor Axis ( AU ), Orbital Period, Radial velocity (m/s), Detectable by:\nQ: What generation of spectrograph is Jupiter detected by?\nA: SELECT Detectable by: FROM 1-10932739-2 WHERE Planet = 'Jupiter'"}
{"text": "table: 1-10932739-2\ncolumns: Planet, Planet Type, Semimajor Axis ( AU ), Orbital Period, Radial velocity (m/s), Detectable by:\nQ: Which planet has an orbital period of 11.86 years?\nA: SELECT Planet FROM 1-10932739-2 WHERE Orbital Period = '11.86 years'"}
{"text": "table: 1-10953197-7\ncolumns: No. in series, No. in season, Title, Director, Writer(s), Original air date, Production code\nQ: who directed the production code 2398204\nA: SELECT Director FROM 1-10953197-7 WHERE Production code = '2398204'"}
{"text": "table: 1-10953197-7\ncolumns: No. in series, No. in season, Title, Director, Writer(s), Original air date, Production code\nQ: when did \"unpleasantville\" air?\nA: SELECT Original air date FROM 1-10953197-7 WHERE Title = '\"Unpleasantville\"'"}
{"text": "table: 1-10960039-1\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What is player Alexis Bwenge's pick number?\nA: SELECT Pick # FROM 1-10960039-1 WHERE Player = 'Alexis Bwenge'"}
{"text": "table: 1-10960039-1\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What player is pick #2?\nA: SELECT Player FROM 1-10960039-1 WHERE Pick # = 2"}
{"text": "table: 1-10960039-1\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: Which player's college is Saskatchewan?\nA: SELECT Player FROM 1-10960039-1 WHERE College = 'Saskatchewan'"}
{"text": "table: 1-10960039-1\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What is McMaster College's pick number?\nA: SELECT MIN Pick # FROM 1-10960039-1 WHERE College = 'McMaster'"}
{"text": "table: 1-10953197-6\ncolumns: No. in series, No. in season, Title, Director, Writer(s), Original air date, Production code\nQ: give the least number of times an episode was shown from 1997-1998\nA: SELECT MIN No. in season FROM 1-10953197-6"}
{"text": "table: 1-10953197-6\ncolumns: No. in series, No. in season, Title, Director, Writer(s), Original air date, Production code\nQ: when was the episode named \"the doctor is in... deep\" first broadcast \nA: SELECT Original air date FROM 1-10953197-6 WHERE Title = '\"The Doctor Is In... Deep\"'"}
{"text": "table: 1-10953197-6\ncolumns: No. in series, No. in season, Title, Director, Writer(s), Original air date, Production code\nQ: how many times does the episode called \"coop de grace\" appear \nA: SELECT COUNT No. in series FROM 1-10953197-6 WHERE Title = '\"Coop de Grace\"'"}
{"text": "table: 1-10953197-6\ncolumns: No. in series, No. in season, Title, Director, Writer(s), Original air date, Production code\nQ: what is season 6 sum of both the number of times processing ID 2397162 was assigned and the number of times chip chalmers managed an episode \nA: SELECT MAX No. in season FROM 1-10953197-6 WHERE Director = 'Chip Chalmers' AND Production code = '2397162'"}
{"text": "table: 1-10966926-2\ncolumns: Round, Choice, Player name, Position, Height, Weight, College\nQ: Which player went to Michigan State?\nA: SELECT Player name FROM 1-10966926-2 WHERE College = 'Michigan State'"}
{"text": "table: 1-10966926-2\ncolumns: Round, Choice, Player name, Position, Height, Weight, College\nQ: Which player went to college in Oklahoma?\nA: SELECT Player name FROM 1-10966926-2 WHERE College = 'Oklahoma'"}
{"text": "table: 1-10966926-2\ncolumns: Round, Choice, Player name, Position, Height, Weight, College\nQ: Which position does Colt Brennan play?\nA: SELECT Position FROM 1-10966926-2 WHERE Player name = 'Colt Brennan'"}
{"text": "table: 1-10966926-2\ncolumns: Round, Choice, Player name, Position, Height, Weight, College\nQ: What is the height of the person that weighs 320 pounds?\nA: SELECT Height FROM 1-10966926-2 WHERE Weight = 320"}
{"text": "table: 1-10975034-4\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: How many colleges have a DB position?\nA: SELECT COUNT College FROM 1-10975034-4 WHERE Position = 'DB'"}
{"text": "table: 1-10975034-4\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What is the maximum number of picks for the CFL team Calgary Stampeders?\nA: SELECT MAX Pick # FROM 1-10975034-4 WHERE CFL Team = 'Calgary Stampeders'"}
{"text": "table: 1-10975034-4\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: How many CFL teams are from York college?\nA: SELECT COUNT CFL Team FROM 1-10975034-4 WHERE College = 'York'"}
{"text": "table: 1-10975034-4\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What CFL teams are part of Simon Fraser college?\nA: SELECT CFL Team FROM 1-10975034-4 WHERE College = 'Simon Fraser'"}
{"text": "table: 1-10975034-4\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: Which players have a pick number of 27?\nA: SELECT Player FROM 1-10975034-4 WHERE Pick # = 27"}
{"text": "table: 1-10960039-6\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: How many times were players named brett ralph were selected?\nA: SELECT COUNT Pick # FROM 1-10960039-6 WHERE Player = 'Brett Ralph'"}
{"text": "table: 1-10960039-6\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What schools did lenard semajuste play for?\nA: SELECT College FROM 1-10960039-6 WHERE Player = 'Lenard Semajuste'"}
{"text": "table: 1-10960039-6\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What is the highest selection number for the saskatchewan roughriders team?\nA: SELECT MAX Pick # FROM 1-10960039-6 WHERE CFL Team = 'Saskatchewan Roughriders'"}
{"text": "table: 1-10960039-6\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: How many fb players were drafted?\nA: SELECT COUNT Pick # FROM 1-10960039-6 WHERE Position = 'FB'"}
{"text": "table: 1-10960039-6\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: How many players played for adams state school?\nA: SELECT COUNT Player FROM 1-10960039-6 WHERE College = 'Adams State'"}
{"text": "table: 1-10960039-6\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What teams drafted players that played for northwood school?\nA: SELECT CFL Team FROM 1-10960039-6 WHERE College = 'Northwood'"}
{"text": "table: 1-10975034-5\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What college did Craig Zimmer go to?\nA: SELECT College FROM 1-10975034-5 WHERE Player = 'Craig Zimmer'"}
{"text": "table: 1-10975034-5\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What is the pick number of regina?\nA: SELECT Pick # FROM 1-10975034-5 WHERE College = 'Regina'"}
{"text": "table: 1-10975034-5\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What is the player who is lb and cfl team is saskatchewan roughriders?\nA: SELECT Player FROM 1-10975034-5 WHERE Position = 'LB' AND CFL Team = 'Saskatchewan Roughriders'"}
{"text": "table: 1-10975034-5\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What is the cfl team that has a position of ol?\nA: SELECT CFL Team FROM 1-10975034-5 WHERE Position = 'OL'"}
{"text": "table: 1-10975034-5\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What is the number of position where the pick number is 43?\nA: SELECT COUNT Position FROM 1-10975034-5 WHERE Pick # = 43"}
{"text": "table: 1-10975034-5\ncolumns: Pick #, CFL Team, Player, Position, College\nQ: What is the cfl team with ryan folk?\nA: SELECT CFL Team FROM 1-10975034-5 WHERE Player = 'Ryan Folk'"}
{"text": "table: 1-10979230-5\ncolumns: Romaji title, Japanese title, Release date, Reference, Oricon\nQ: What release date is when kids-270 is a reference? \nA: SELECT Release date FROM 1-10979230-5 WHERE Reference = 'KIDS-270'"}
{"text": "table: 1-10979230-5\ncolumns: Romaji title, Japanese title, Release date, Reference, Oricon\nQ: what is the title where romaji is titles da.i.su.ki\nA: SELECT Japanese title FROM 1-10979230-5 WHERE Romaji title = 'Da.i.su.ki'"}
{"text": "table: 1-10979230-5\ncolumns: Romaji title, Japanese title, Release date, Reference, Oricon\nQ: what are the title in japanese where the reference is kids-430?\nA: SELECT Japanese title FROM 1-10979230-5 WHERE Reference = 'KIDS-430'"}
{"text": "table: 1-10979230-5\ncolumns: Romaji title, Japanese title, Release date, Reference, Oricon\nQ: who is the reference when romaji title is heartbreak sniper?\nA: SELECT Reference FROM 1-10979230-5 WHERE Romaji title = 'Heartbreak Sniper'"}
{"text": "table: 1-10979230-4\ncolumns: Romaji title, Japanese title, Release date, Reference, Oricon\nQ: What rank is \u611b\u306e\u30d0\u30ab on the Japanese singles chart?\nA: SELECT COUNT Oricon FROM 1-10979230-4 WHERE Japanese title = '\u611b\u306e\u30d0\u30ab'"}
{"text": "table: 1-10979230-4\ncolumns: Romaji title, Japanese title, Release date, Reference, Oricon\nQ: How many songs have mi-chemin as their Japanese name and romanji name?\nA: SELECT COUNT Romaji title FROM 1-10979230-4 WHERE Japanese title = 'Mi-Chemin'"}
{"text": "table: 1-1099080-1\ncolumns: Condition, Prothrombin time, Partial thromboplastin time, Bleeding time, Platelet count\nQ: What was the  partial thromboplastin time for factor x deficiency as seen in amyloid purpura\nA: SELECT Partial thromboplastin time FROM 1-1099080-1 WHERE Condition = 'Factor X deficiency as seen in amyloid purpura'"}
{"text": "table: 1-1099080-1\ncolumns: Condition, Prothrombin time, Partial thromboplastin time, Bleeding time, Platelet count\nQ: How many conditions have an unaffected prothrombin time and a prolonged bleeding time\nA: SELECT COUNT Condition FROM 1-1099080-1 WHERE Prothrombin time = 'Unaffected' AND Bleeding time = 'Prolonged'"}
{"text": "table: 1-1099080-1\ncolumns: Condition, Prothrombin time, Partial thromboplastin time, Bleeding time, Platelet count\nQ: What was the bleeding time for the factor x deficiency as seen in amyloid purpura\nA: SELECT Bleeding time FROM 1-1099080-1 WHERE Condition = 'Factor X deficiency as seen in amyloid purpura'"}
{"text": "table: 1-1099080-1\ncolumns: Condition, Prothrombin time, Partial thromboplastin time, Bleeding time, Platelet count\nQ: What conditions had both prolonged bleeding times and prolonged partial thromboplastin times\nA: SELECT Condition FROM 1-1099080-1 WHERE Partial thromboplastin time = 'Prolonged' AND Bleeding time = 'Prolonged'"}
{"text": "table: 1-1099080-1\ncolumns: Condition, Prothrombin time, Partial thromboplastin time, Bleeding time, Platelet count\nQ: What was the bleeding time for  factor xii deficiency\nA: SELECT Bleeding time FROM 1-1099080-1 WHERE Condition = 'Factor XII deficiency'"}
{"text": "table: 1-1099080-1\ncolumns: Condition, Prothrombin time, Partial thromboplastin time, Bleeding time, Platelet count\nQ: What were the bleeding times when both the platelet count was unaffected and the partial thromboplastin time was unaffected\nA: SELECT Bleeding time FROM 1-1099080-1 WHERE Partial thromboplastin time = 'Unaffected' AND Platelet count = 'Unaffected'"}
{"text": "table: 1-11019212-1\ncolumns: Location, Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday\nQ: what's the\u00a0tuesday\u00a0time with\u00a0location\u00a0being millhopper\nA: SELECT Tuesday FROM 1-11019212-1 WHERE Location = 'Millhopper'"}
{"text": "table: 1-11019212-1\ncolumns: Location, Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday\nQ: what's the\u00a0wednesday time\u00a0with\u00a0monday\u00a0being 10:00-8:00\nA: SELECT Wednesday FROM 1-11019212-1 WHERE Monday = '10:00-8:00'"}
{"text": "table: 1-11019212-1\ncolumns: Location, Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday\nQ: what's the\u00a0thursday\u00a0time with\u00a0location\u00a0being hawthorne\nA: SELECT Thursday FROM 1-11019212-1 WHERE Location = 'Hawthorne'"}
{"text": "table: 1-11019212-1\ncolumns: Location, Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday\nQ: what's the\u00a0saturday\u00a0time with\u00a0wednesday\u00a0being 10:00-5:00\nA: SELECT Saturday FROM 1-11019212-1 WHERE Wednesday = '10:00-5:00'"}
{"text": "table: 1-11019212-1\ncolumns: Location, Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday\nQ: what's the\u00a0thursday\u00a0time with\u00a0sunday\u00a0being 1:00-5:00 and\u00a0tuesday\u00a0being 1:00-7:00\nA: SELECT Thursday FROM 1-11019212-1 WHERE Sunday = '1:00-5:00' AND Tuesday = '1:00-7:00'"}
{"text": "table: 1-11019212-1\ncolumns: Location, Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday\nQ: what's the\u00a0monday\u00a0time with\u00a0tuesday\u00a0being 9:00-6:00\nA: SELECT Monday FROM 1-11019212-1 WHERE Tuesday = '9:00-6:00'"}
{"text": "table: 1-11056278-3\ncolumns: Rnd, Race Name, Pole position, Fastest lap, Winning driver, Winning team, Report\nQ: What are all the reports where Paul Tracy had the fastest lap?\nA: SELECT Report FROM 1-11056278-3 WHERE Fastest lap = 'Paul Tracy'"}
{"text": "table: 1-11056278-3\ncolumns: Rnd, Race Name, Pole position, Fastest lap, Winning driver, Winning team, Report\nQ: Who drove the fastest lap at the Tenneco Automotive Grand Prix of Detroit?\nA: SELECT Fastest lap FROM 1-11056278-3 WHERE Race Name = 'Tenneco Automotive Grand Prix of Detroit'"}
{"text": "table: 1-11056278-3\ncolumns: Rnd, Race Name, Pole position, Fastest lap, Winning driver, Winning team, Report\nQ: Who had the fastest lap in the races won by Max Papis?\nA: SELECT Fastest lap FROM 1-11056278-3 WHERE Winning driver = 'Max Papis'"}
{"text": "table: 1-11056278-3\ncolumns: Rnd, Race Name, Pole position, Fastest lap, Winning driver, Winning team, Report\nQ: In Round 6, how many winning drivers were there?\nA: SELECT COUNT Winning driver FROM 1-11056278-3 WHERE Rnd = 6"}
{"text": "table: 1-1104312-5\ncolumns: English name, Original name, Area in km\u00b2, Population at 2010 Census, Number of settlements and villages\nQ: What are the original names of the districts where the population in the 2010 census was 210450?\nA: SELECT Original name FROM 1-1104312-5 WHERE Population at 2010 Census = 210450"}
{"text": "table: 1-1104312-5\ncolumns: English name, Original name, Area in km\u00b2, Population at 2010 Census, Number of settlements and villages\nQ: What is the original name of the district with the current English name of South Bogor?\nA: SELECT Original name FROM 1-1104312-5 WHERE English name = 'South Bogor'"}
{"text": "table: 1-1104312-5\ncolumns: English name, Original name, Area in km\u00b2, Population at 2010 Census, Number of settlements and villages\nQ: What is the listed population from the 2010 census of West Bogor?\nA: SELECT MIN Population at 2010 Census FROM 1-1104312-5 WHERE English name = 'West Bogor'"}
{"text": "table: 1-1104312-5\ncolumns: English name, Original name, Area in km\u00b2, Population at 2010 Census, Number of settlements and villages\nQ: How many districts have an area of 17.72 KM2?\nA: SELECT COUNT English name FROM 1-1104312-5 WHERE Area in km\u00b2 = '17.72'"}
{"text": "table: 1-1104312-5\ncolumns: English name, Original name, Area in km\u00b2, Population at 2010 Census, Number of settlements and villages\nQ: What is the area in km2 for the district whose original name was Kecamatan Bogor Timur?\nA: SELECT Area in km\u00b2 FROM 1-1104312-5 WHERE Original name = 'Kecamatan Bogor Timur'"}
{"text": "table: 1-11066073-1\ncolumns: Pilot car No., Colour, Serial No., Engine No., Registration No.\nQ: What is the number of colour with the regisration number of mg-509?\nA: SELECT COUNT Colour FROM 1-11066073-1 WHERE Registration No. = 'MG-509'"}
{"text": "table: 1-11058032-1\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: What is the title of the episode directed by Mark Tinker?\nA: SELECT Title FROM 1-11058032-1 WHERE Directed by = 'Mark Tinker'"}
{"text": "table: 1-11058032-1\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: What episode in the season was directed by Jeff Melman?\nA: SELECT MIN No. in season FROM 1-11058032-1 WHERE Directed by = 'Jeff Melman'"}
{"text": "table: 1-11058032-1\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: How many episodes had 16.03 million viewers?\nA: SELECT COUNT No. in series FROM 1-11058032-1 WHERE U.S. viewers (millions) = '16.03'"}
{"text": "table: 1-11071897-1\ncolumns: Interregnum began, Interregnum ended, Duration, Count Palatine of Saxony, Count Palatine of the Rhine\nQ: What is the number of interregnum for duration 3 months, 6 days?\nA: SELECT COUNT Interregnum ended FROM 1-11071897-1 WHERE Duration = '3 months, 6 days'"}
{"text": "table: 1-11075747-4\ncolumns: Series #, Episode #, Title, Directed by, Written by, Original air date\nQ: Who directed Episode 8?\nA: SELECT Directed by FROM 1-11075747-4 WHERE Episode # = 8"}
{"text": "table: 1-11075747-4\ncolumns: Series #, Episode #, Title, Directed by, Written by, Original air date\nQ: Who directed the episode called \"Tell-tale Heart\"?\nA: SELECT Directed by FROM 1-11075747-4 WHERE Title = '\"Tell-Tale Heart\"'"}
{"text": "table: 1-11075747-4\ncolumns: Series #, Episode #, Title, Directed by, Written by, Original air date\nQ: What was the original air date for Series 36?\nA: SELECT Original air date FROM 1-11075747-4 WHERE Series # = 36"}
{"text": "table: 1-11075747-4\ncolumns: Series #, Episode #, Title, Directed by, Written by, Original air date\nQ: Who wrote Series 38?\nA: SELECT Written by FROM 1-11075747-4 WHERE Series # = 38"}
{"text": "table: 1-1108394-24\ncolumns: 1973 Democratic initial primary, Manhattan, The Bronx, Brooklyn, Queens, Richmond [Staten Is.], Total, %\nQ: What is the percentage for manhattan 45,901?\nA: SELECT COUNT % FROM 1-1108394-24 WHERE Manhattan = '45,901'"}
{"text": "table: 1-1108394-24\ncolumns: 1973 Democratic initial primary, Manhattan, The Bronx, Brooklyn, Queens, Richmond [Staten Is.], Total, %\nQ: Who won the 1973 democratic initial primary for queens of 19%?\nA: SELECT 1973 Democratic initial primary FROM 1-1108394-24 WHERE Queens = '19%'"}
{"text": "table: 1-1108394-24\ncolumns: 1973 Democratic initial primary, Manhattan, The Bronx, Brooklyn, Queens, Richmond [Staten Is.], Total, %\nQ: What is the manhattan for richmond 35%?\nA: SELECT Manhattan FROM 1-1108394-24 WHERE Richmond [Staten Is.] = '35%'"}
{"text": "table: 1-1108394-24\ncolumns: 1973 Democratic initial primary, Manhattan, The Bronx, Brooklyn, Queens, Richmond [Staten Is.], Total, %\nQ: What is the queens where richmond staten is 42%?\nA: SELECT Queens FROM 1-1108394-24 WHERE Richmond [Staten Is.] = '42%'"}
{"text": "table: 1-1108394-43\ncolumns: 1932 (before recount), party, Manhattan, The Bronx, Brooklyn, Queens, Richmond [Staten Is.], Total, %\nQ: what's the\u00a0party\u00a0with\u00a0brooklyn\u00a0value of 51.0%\nA: SELECT party FROM 1-1108394-43 WHERE Brooklyn = '51.0%'"}
{"text": "table: 1-1108394-43\ncolumns: 1932 (before recount), party, Manhattan, The Bronx, Brooklyn, Queens, Richmond [Staten Is.], Total, %\nQ: what's the\u00a0brooklyn\u00a0with\u00a0queens\u00a0value of 16.8%\nA: SELECT Brooklyn FROM 1-1108394-43 WHERE Queens = '16.8%'"}
{"text": "table: 1-1108394-43\ncolumns: 1932 (before recount), party, Manhattan, The Bronx, Brooklyn, Queens, Richmond [Staten Is.], Total, %\nQ: what is the minimum total\nA: SELECT MIN Total FROM 1-1108394-43"}
{"text": "table: 1-1108394-43\ncolumns: 1932 (before recount), party, Manhattan, The Bronx, Brooklyn, Queens, Richmond [Staten Is.], Total, %\nQ: what's the\u00a0%\u00a0with\u00a0total\u00a0value of 249887 and\u00a0queens\u00a0value of 6.8%\nA: SELECT % FROM 1-1108394-43 WHERE Total = 249887 AND Queens = '6.8%'"}
{"text": "table: 1-11094950-1\ncolumns: Team, Location, Joined, Conference, Division, Previous Conference\nQ: Which teams were in the central division and located in livonia?\nA: SELECT Team FROM 1-11094950-1 WHERE Division = 'Central' AND Location = 'Livonia'"}
{"text": "table: 1-11094950-1\ncolumns: Team, Location, Joined, Conference, Division, Previous Conference\nQ: Which teams are located in highland township?\nA: SELECT Team FROM 1-11094950-1 WHERE Location = 'Highland Township'"}
{"text": "table: 1-11094950-1\ncolumns: Team, Location, Joined, Conference, Division, Previous Conference\nQ: What conference was the churchill chargers team in?\nA: SELECT Conference FROM 1-11094950-1 WHERE Team = 'Churchill Chargers'"}
{"text": "table: 1-11111116-7\ncolumns: No. in season, No. in series, Title, Directed by, Written by, Original air date, U.S. viewers (million)\nQ: What was the titles of the episodes written by ken lazebnik?\nA: SELECT Title FROM 1-11111116-7 WHERE Written by = 'Ken LaZebnik'"}
{"text": "table: 1-11111116-7\ncolumns: No. in season, No. in series, Title, Directed by, Written by, Original air date, U.S. viewers (million)\nQ: Who directed an episode that had 2.81 million U.S. viewers?\nA: SELECT Directed by FROM 1-11111116-7 WHERE U.S. viewers (million) = '2.81'"}
{"text": "table: 1-11111116-7\ncolumns: No. in season, No. in series, Title, Directed by, Written by, Original air date, U.S. viewers (million)\nQ: What were the names of the episodes that had 3.02 million U.S. viewers?\nA: SELECT Title FROM 1-11111116-7 WHERE U.S. viewers (million) = '3.02'"}
{"text": "table: 1-11111116-7\ncolumns: No. in season, No. in series, Title, Directed by, Written by, Original air date, U.S. viewers (million)\nQ: What were the original air dates of the episode named \"winds of war\"?\nA: SELECT Original air date FROM 1-11111116-7 WHERE Title = '\"Winds of War\"'"}
{"text": "table: 1-11111116-7\ncolumns: No. in season, No. in series, Title, Directed by, Written by, Original air date, U.S. viewers (million)\nQ: Who directed episodes that had 2.61 million U.S. viewers?\nA: SELECT Directed by FROM 1-11111116-7 WHERE U.S. viewers (million) = '2.61'"}
{"text": "table: 1-11111116-8\ncolumns: No. in season, No. in series, Title, Directed by, Written by, Original air date, U.S. viewers (million)\nQ: How many millions of U.S. viewers watched \"Brace for Impact\"?\nA: SELECT U.S. viewers (million) FROM 1-11111116-8 WHERE Title = '\"Brace for Impact\"'"}
{"text": "table: 1-11111116-8\ncolumns: No. in season, No. in series, Title, Directed by, Written by, Original air date, U.S. viewers (million)\nQ: How many millions of U.S. viewers watched the episode that first aired on March 31, 2013?\nA: SELECT U.S. viewers (million) FROM 1-11111116-8 WHERE Original air date = 'March 31, 2013'"}
{"text": "table: 1-11111116-8\ncolumns: No. in season, No. in series, Title, Directed by, Written by, Original air date, U.S. viewers (million)\nQ: Who wrote the episodes that were viewed by 2.12 million viewers?\nA: SELECT Written by FROM 1-11111116-8 WHERE U.S. viewers (million) = '2.12'"}
{"text": "table: 1-11111116-6\ncolumns: No. in season, No. in series, Title, Directed by, Written by, Original air date, U.S. viewers (million)\nQ: The episode written by Rebecca Dameron aired on what date? \nA: SELECT Original air date FROM 1-11111116-6 WHERE Written by = 'Rebecca Dameron'"}
{"text": "table: 1-11111116-6\ncolumns: No. in season, No. in series, Title, Directed by, Written by, Original air date, U.S. viewers (million)\nQ: Which episode in the series drew 3.6 million U.S. viewers? \nA: SELECT MIN No. in series FROM 1-11111116-6 WHERE U.S. viewers (million) = '3.6'"}
{"text": "table: 1-11111116-6\ncolumns: No. in season, No. in series, Title, Directed by, Written by, Original air date, U.S. viewers (million)\nQ: Who wrote the episode that aired on April 17, 2011? \nA: SELECT Written by FROM 1-11111116-6 WHERE Original air date = 'April 17, 2011'"}
{"text": "table: 1-11111116-6\ncolumns: No. in season, No. in series, Title, Directed by, Written by, Original air date, U.S. viewers (million)\nQ: How many times did episode 79 originally air? \nA: SELECT COUNT Original air date FROM 1-11111116-6 WHERE No. in series = 79"}
{"text": "table: 1-11111116-6\ncolumns: No. in season, No. in series, Title, Directed by, Written by, Original air date, U.S. viewers (million)\nQ: How many millions of views in the country watched \"Line of Departure\"?\nA: SELECT U.S. viewers (million) FROM 1-11111116-6 WHERE Title = '\"Line of Departure\"'"}
{"text": "table: 1-11148572-1\ncolumns: Season, MLS Cup Winner, MLS Cup Runner-Up, MLS Supporters Shield Winner, MLS Supporters Shield Runner-Up\nQ: What is the name of the shield winner in which the mls cup winner and mls cup runner up is colorado rapids?\nA: SELECT MLS Cup Winner FROM 1-11148572-1 WHERE MLS Cup Runner-Up = 'Colorado Rapids'"}
{"text": "table: 1-11148572-1\ncolumns: Season, MLS Cup Winner, MLS Cup Runner-Up, MLS Supporters Shield Winner, MLS Supporters Shield Runner-Up\nQ: What is the name of the shield winner in which the mls cup winner and mls supporters shield runner up is Chivas usa?\nA: SELECT MLS Cup Winner FROM 1-11148572-1 WHERE MLS Supporters Shield Runner-Up = 'Chivas USA'"}
{"text": "table: 1-11148572-1\ncolumns: Season, MLS Cup Winner, MLS Cup Runner-Up, MLS Supporters Shield Winner, MLS Supporters Shield Runner-Up\nQ: who is the of the shield winnerin which the mls cup runner-up and mls cup winner is real salt lake?\nA: SELECT MLS Cup Runner-Up FROM 1-11148572-1 WHERE MLS Cup Winner = 'Real Salt Lake'"}
{"text": "table: 1-11148572-1\ncolumns: Season, MLS Cup Winner, MLS Cup Runner-Up, MLS Supporters Shield Winner, MLS Supporters Shield Runner-Up\nQ: Which shield winner has the mls cup runner up and the season is 2000?\nA: SELECT MLS Cup Runner-Up FROM 1-11148572-1 WHERE Season = 2000"}
{"text": "table: 1-1112176-1\ncolumns: Season, Division, League Apps (Sub), League Goals, FA Cup Apps (Sub), FA Cup Goals, FL Cup Apps (Sub), FL Cup Goals, Other Apps, Other Goals, Total Apps (Sub), Total Goals\nQ: League apps (sub) maximum?\nA: SELECT MAX League Apps (Sub) FROM 1-1112176-1"}
{"text": "table: 1-1112176-1\ncolumns: Season, Division, League Apps (Sub), League Goals, FA Cup Apps (Sub), FA Cup Goals, FL Cup Apps (Sub), FL Cup Goals, Other Apps, Other Goals, Total Apps (Sub), Total Goals\nQ: When total goals is 11 what was the league apps (sub)?\nA: SELECT MAX League Apps (Sub) FROM 1-1112176-1 WHERE Total Goals = 11"}
{"text": "table: 1-11129123-1\ncolumns: Episode Air Date, Audition City, Date, First Audition Venue, Callback Date, Callback Venue, Golden Tickets\nQ: Which city had the charleston area convention center as its callback location\nA: SELECT Audition City FROM 1-11129123-1 WHERE Callback Venue = 'Charleston Area Convention Center'"}
{"text": "table: 1-11129123-1\ncolumns: Episode Air Date, Audition City, Date, First Audition Venue, Callback Date, Callback Venue, Golden Tickets\nQ: When did the callbacks from  rancho bernardo inn air\nA: SELECT Episode Air Date FROM 1-11129123-1 WHERE Callback Venue = 'Rancho Bernardo Inn'"}
{"text": "table: 1-11147852-1\ncolumns: City of license/Market, Station, Channel TV ( DT ), Year of affiliation, Owned since\nQ: The station located in Albuquerque has been owned since what year?\nA: SELECT Owned since FROM 1-11147852-1 WHERE City of license/Market = 'Albuquerque'"}
{"text": "table: 1-11147852-1\ncolumns: City of license/Market, Station, Channel TV ( DT ), Year of affiliation, Owned since\nQ: What channels have stations that were affiliated in 2002?\nA: SELECT Channel TV ( DT ) FROM 1-11147852-1 WHERE Year of affiliation = '2002'"}
{"text": "table: 1-11147852-1\ncolumns: City of license/Market, Station, Channel TV ( DT ), Year of affiliation, Owned since\nQ: What market is KTFK-DT in?\nA: SELECT City of license/Market FROM 1-11147852-1 WHERE Station = 'KTFK-DT'"}
{"text": "table: 1-11167610-1\ncolumns: Trim, Engine, Turbo, Fuel Delivery, Power, Torque, Transmission, Performance\nQ:  what's the\u00a0engine\u00a0where\u00a0performance\u00a0is 0\u2013100km/h: 10.5s, vmax km/h (mph)\nA: SELECT Engine FROM 1-11167610-1 WHERE Performance = '0\u2013100km/h: 10.5s, VMax km/h (mph)'"}
{"text": "table: 1-11167610-1\ncolumns: Trim, Engine, Turbo, Fuel Delivery, Power, Torque, Transmission, Performance\nQ:  what's the\u00a0turbo\u00a0where\u00a0trim\u00a0is 2.0 20v\nA: SELECT Turbo FROM 1-11167610-1 WHERE Trim = '2.0 20v'"}
{"text": "table: 1-11167610-1\ncolumns: Trim, Engine, Turbo, Fuel Delivery, Power, Torque, Transmission, Performance\nQ:  what's the\u00a0torque\u00a0where\u00a0performance\u00a0is 0\u2013100km/h: 7.5s auto, vmax: km/h (mph)\nA: SELECT Torque FROM 1-11167610-1 WHERE Performance = '0\u2013100km/h: 7.5s auto, VMax: km/h (mph)'"}
{"text": "table: 1-11167610-1\ncolumns: Trim, Engine, Turbo, Fuel Delivery, Power, Torque, Transmission, Performance\nQ:  what's the\u00a0transmission\u00a0where\u00a0turbo\u00a0is yes (mitsubishi td04-16t )\nA: SELECT Transmission FROM 1-11167610-1 WHERE Turbo = 'Yes (Mitsubishi TD04-16t )'"}
{"text": "table: 1-11167610-1\ncolumns: Trim, Engine, Turbo, Fuel Delivery, Power, Torque, Transmission, Performance\nQ:  what's the\u00a0fuel delivery\u00a0where\u00a0power\u00a0is hp (kw) @6500 rpm\nA: SELECT Fuel Delivery FROM 1-11167610-1 WHERE Power = 'hp (kW) @6500 rpm'"}
{"text": "table: 1-11167610-1\ncolumns: Trim, Engine, Turbo, Fuel Delivery, Power, Torque, Transmission, Performance\nQ: \" what's the engine with turbo being yes (mitsubishi td04-15g ) \"\nA: SELECT Engine FROM 1-11167610-1 WHERE Turbo = 'Yes (Mitsubishi TD04-15g )'"}
{"text": "table: 1-11173827-1\ncolumns: Rank, English title, Chinese title, Average, Peak, Premiere, Finale, HK viewers\nQ: What is the english title that has finale as 33 and peak as 42?\nA: SELECT English title FROM 1-11173827-1 WHERE Finale = 33 AND Peak = 42"}
{"text": "table: 1-11173827-1\ncolumns: Rank, English title, Chinese title, Average, Peak, Premiere, Finale, HK viewers\nQ: What is the english title where the premiere is less than 30.0 and the finale is bigger than 36.0?\nA: SELECT English title FROM 1-11173827-1 WHERE Premiere < 30.0 AND Finale > 36.0"}
{"text": "table: 1-11173827-1\ncolumns: Rank, English title, Chinese title, Average, Peak, Premiere, Finale, HK viewers\nQ: What is the rank of the chinese title \u7de3\u4f86\u81ea\u6709\u6a5f?\nA: SELECT Rank FROM 1-11173827-1 WHERE Chinese title = '\u7de3\u4f86\u81ea\u6709\u6a5f'"}
{"text": "table: 1-11173827-1\ncolumns: Rank, English title, Chinese title, Average, Peak, Premiere, Finale, HK viewers\nQ: What amount is the number of hk viewers where chinese title is \u5341\u5144\u5f1f?\nA: SELECT HK viewers FROM 1-11173827-1 WHERE Chinese title = '\u5341\u5144\u5f1f'"}
{"text": "table: 1-11178271-1\ncolumns: #, Episode, Air Date, Rating, Share, 18\u201349 (Rating/Share), Viewers (m), Weekly Rank\nQ: What is the weekly rank with an air date is november 12, 2007?\nA: SELECT Weekly Rank FROM 1-11178271-1 WHERE Air Date = 'November 12, 2007'"}
{"text": "table: 1-11178271-1\ncolumns: #, Episode, Air Date, Rating, Share, 18\u201349 (Rating/Share), Viewers (m), Weekly Rank\nQ: What is the air date of the episode \"blowback\"?\nA: SELECT Air Date FROM 1-11178271-1 WHERE Episode = '\"Blowback\"'"}
{"text": "table: 1-11178271-1\ncolumns: #, Episode, Air Date, Rating, Share, 18\u201349 (Rating/Share), Viewers (m), Weekly Rank\nQ: What is the lowest weekly rank with an air date of november 26, 2007?\nA: SELECT MIN Weekly Rank FROM 1-11178271-1 WHERE Air Date = 'November 26, 2007'"}
{"text": "table: 1-11178271-1\ncolumns: #, Episode, Air Date, Rating, Share, 18\u201349 (Rating/Share), Viewers (m), Weekly Rank\nQ: What is the episode where 18-49 has a rating/share of 3.5/9\nA: SELECT Episode FROM 1-11178271-1 WHERE 18\u201349 (Rating/Share) = '3.5/9'"}
{"text": "table: 1-11178271-1\ncolumns: #, Episode, Air Date, Rating, Share, 18\u201349 (Rating/Share), Viewers (m), Weekly Rank\nQ: What is the viewers where the rating is 5.3?\nA: SELECT Viewers (m) FROM 1-11178271-1 WHERE Rating = '5.3'"}
{"text": "table: 1-11178271-1\ncolumns: #, Episode, Air Date, Rating, Share, 18\u201349 (Rating/Share), Viewers (m), Weekly Rank\nQ: What is the 18-49 rating/share where the viewers is 5.61?\nA: SELECT 18\u201349 (Rating/Share) FROM 1-11178271-1 WHERE Viewers (m) = '5.61'"}
{"text": "table: 1-11206787-5\ncolumns: Team, Stadium, Capacity, Highest, Lowest, Average\nQ: What is the highest of balmoor/\nA: SELECT Highest FROM 1-11206787-5 WHERE Stadium = 'Balmoor'"}
{"text": "table: 1-11206787-5\ncolumns: Team, Stadium, Capacity, Highest, Lowest, Average\nQ: What is the number of capacity at somerset park?\nA: SELECT COUNT Capacity FROM 1-11206787-5 WHERE Stadium = 'Somerset Park'"}
{"text": "table: 1-11206787-5\ncolumns: Team, Stadium, Capacity, Highest, Lowest, Average\nQ: What is the minimum capacity where airdrie united is?\nA: SELECT MIN Capacity FROM 1-11206787-5 WHERE Team = 'Airdrie United'"}
{"text": "table: 1-11206787-5\ncolumns: Team, Stadium, Capacity, Highest, Lowest, Average\nQ: What is the stadium for alloa athletic?\nA: SELECT Stadium FROM 1-11206787-5 WHERE Team = 'Alloa Athletic'"}
{"text": "table: 1-11206787-5\ncolumns: Team, Stadium, Capacity, Highest, Lowest, Average\nQ: What is the highest of ayr united?\nA: SELECT MIN Highest FROM 1-11206787-5 WHERE Team = 'Ayr United'"}
{"text": "table: 1-11206787-5\ncolumns: Team, Stadium, Capacity, Highest, Lowest, Average\nQ: What is the average?\nA: SELECT MIN Average FROM 1-11206787-5"}
{"text": "table: 1-11190568-7\ncolumns: Team, Outgoing manager, Manner of departure, Date of vacancy, Replaced by, Date of appointment, Position\nQ: When are team Galway's dates of appointment?\nA: SELECT Date of appointment FROM 1-11190568-7 WHERE Team = 'Galway'"}
{"text": "table: 1-11190568-7\ncolumns: Team, Outgoing manager, Manner of departure, Date of vacancy, Replaced by, Date of appointment, Position\nQ: When are the vacancy dates for outgoing manager Damien Fox?\nA: SELECT Date of vacancy FROM 1-11190568-7 WHERE Outgoing manager = 'Damien Fox'"}
{"text": "table: 1-11190568-7\ncolumns: Team, Outgoing manager, Manner of departure, Date of vacancy, Replaced by, Date of appointment, Position\nQ: When is the date of vacancy of Davy Fitzgerald being a replacement?\nA: SELECT Date of vacancy FROM 1-11190568-7 WHERE Replaced by = 'Davy FitzGerald'"}
{"text": "table: 1-11190568-7\ncolumns: Team, Outgoing manager, Manner of departure, Date of vacancy, Replaced by, Date of appointment, Position\nQ: Which team has the outgoing manager John Meyler?\nA: SELECT Team FROM 1-11190568-7 WHERE Outgoing manager = 'John Meyler'"}
{"text": "table: 1-11200856-1\ncolumns: Hand, 1 credit, 2 credits, 3 credits, 4 credits, 5 credits\nQ: How many times is 3 credits 180?\nA: SELECT COUNT 1 credit FROM 1-11200856-1 WHERE 3 credits = 180"}
{"text": "table: 1-11200856-1\ncolumns: Hand, 1 credit, 2 credits, 3 credits, 4 credits, 5 credits\nQ: What is the hand for 4 credits is 1600?\nA: SELECT Hand FROM 1-11200856-1 WHERE 4 credits = 1600"}
{"text": "table: 1-11200856-1\ncolumns: Hand, 1 credit, 2 credits, 3 credits, 4 credits, 5 credits\nQ: How many 3 credits are there with 5 credits of 5?\nA: SELECT COUNT 3 credits FROM 1-11200856-1 WHERE 5 credits = '5'"}
{"text": "table: 1-11200856-1\ncolumns: Hand, 1 credit, 2 credits, 3 credits, 4 credits, 5 credits\nQ: How many 4 credits is the hand two pair?\nA: SELECT COUNT 4 credits FROM 1-11200856-1 WHERE Hand = 'Two pair'"}
{"text": "table: 1-11210576-3\ncolumns: Character, Position, Actor, First Episode, Final Episode, Duration, Final Episode Count\nQ: What duration is listed for Christian de la Fuente?\nA: SELECT Duration FROM 1-11210576-3 WHERE Actor = 'Christian de la Fuente'"}
{"text": "table: 1-11210576-3\ncolumns: Character, Position, Actor, First Episode, Final Episode, Duration, Final Episode Count\nQ: What was the final episode for Dea Agent?\nA: SELECT Final Episode FROM 1-11210576-3 WHERE Position = 'DEA Agent'"}
{"text": "table: 1-11207040-6\ncolumns: Team, Outgoing manager, Manner of departure, Date of vacancy, Replaced by, Date of appointment\nQ: What days is greenock morton vacant?\nA: SELECT Date of vacancy FROM 1-11207040-6 WHERE Team = 'Greenock Morton'"}
{"text": "table: 1-11207040-6\ncolumns: Team, Outgoing manager, Manner of departure, Date of vacancy, Replaced by, Date of appointment\nQ: What are the dates of the outgoing manager colin hendry does appointments? \nA: SELECT Date of appointment FROM 1-11207040-6 WHERE Outgoing manager = 'Colin Hendry'"}
{"text": "table: 1-11207040-6\ncolumns: Team, Outgoing manager, Manner of departure, Date of vacancy, Replaced by, Date of appointment\nQ: What teams does jim mcinally manage?\nA: SELECT Team FROM 1-11207040-6 WHERE Outgoing manager = 'Jim McInally'"}
{"text": "table: 1-11207040-6\ncolumns: Team, Outgoing manager, Manner of departure, Date of vacancy, Replaced by, Date of appointment\nQ: What days are vacant that were replaced by john brown?\nA: SELECT Date of vacancy FROM 1-11207040-6 WHERE Replaced by = 'John Brown'"}
{"text": "table: 1-11206916-2\ncolumns: Team, Outgoing manager, Manner of departure, Date of vacancy, Replaced by, Date of appointment\nQ: What manner of departure is listed with an appointment date of 13 march 2008\nA: SELECT Manner of departure FROM 1-11206916-2 WHERE Date of appointment = '13 March 2008'"}
{"text": "table: 1-11206916-2\ncolumns: Team, Outgoing manager, Manner of departure, Date of vacancy, Replaced by, Date of appointment\nQ: What is the date of appointment for outgoing manager Campbell Money\nA: SELECT Date of appointment FROM 1-11206916-2 WHERE Outgoing manager = 'Campbell Money'"}
{"text": "table: 1-11207040-5\ncolumns: Team, Stadium, Capacity, Highest, Lowest, Average\nQ: What is the lowest attendance that East End Park has ever had?\nA: SELECT MIN Lowest FROM 1-11207040-5 WHERE Stadium = 'East End Park'"}
{"text": "table: 1-11207040-5\ncolumns: Team, Stadium, Capacity, Highest, Lowest, Average\nQ: What team plays at Palmerston Park?\nA: SELECT Team FROM 1-11207040-5 WHERE Stadium = 'Palmerston Park'"}
{"text": "table: 1-11207040-5\ncolumns: Team, Stadium, Capacity, Highest, Lowest, Average\nQ: What is the lowest attandance recorded at Cappielow?\nA: SELECT MIN Lowest FROM 1-11207040-5 WHERE Stadium = 'Cappielow'"}
{"text": "table: 1-11207040-5\ncolumns: Team, Stadium, Capacity, Highest, Lowest, Average\nQ: What is the highest attendance at a game played by St. Johnstone?\nA: SELECT MAX Highest FROM 1-11207040-5 WHERE Team = 'St. Johnstone'"}
{"text": "table: 1-11207040-5\ncolumns: Team, Stadium, Capacity, Highest, Lowest, Average\nQ: What is the highest attandence at a Hamilton Academical game?\nA: SELECT MIN Highest FROM 1-11207040-5 WHERE Team = 'Hamilton Academical'"}
{"text": "table: 1-11214772-1\ncolumns: Year, Champion, Score, Runner-Up, Location, Semi-Finalist #1, Semi-Finalist #2\nQ:  who is the\u00a0champion\u00a0where\u00a0semi-finalist #2\u00a0is na and\u00a0location\u00a0is morrisville, nc\nA: SELECT Champion FROM 1-11214772-1 WHERE Semi-Finalist #2 = 'NA' AND Location = 'Morrisville, NC'"}
{"text": "table: 1-11214772-1\ncolumns: Year, Champion, Score, Runner-Up, Location, Semi-Finalist #1, Semi-Finalist #2\nQ:  what's the\u00a0score\u00a0where\u00a0year\u00a0is 2007\nA: SELECT Score FROM 1-11214772-1 WHERE Year = '2007'"}
{"text": "table: 1-11214772-1\ncolumns: Year, Champion, Score, Runner-Up, Location, Semi-Finalist #1, Semi-Finalist #2\nQ: what is the total number of\u00a0semi-finalist #2\u00a0where\u00a0runner-up\u00a0is east carolina\nA: SELECT COUNT Semi-Finalist #2 FROM 1-11214772-1 WHERE Runner-Up = 'East Carolina'"}
{"text": "table: 1-11214772-1\ncolumns: Year, Champion, Score, Runner-Up, Location, Semi-Finalist #1, Semi-Finalist #2\nQ:  who is the\u00a0semi-finalist #1\u00a0where\u00a0runner-up\u00a0is elon university\nA: SELECT Semi-Finalist #1 FROM 1-11214772-1 WHERE Runner-Up = 'Elon University'"}
{"text": "table: 1-11214772-1\ncolumns: Year, Champion, Score, Runner-Up, Location, Semi-Finalist #1, Semi-Finalist #2\nQ:  who is the\u00a0runner-up\u00a0where\u00a0year\u00a0is 2004 and\u00a0champion\u00a0is north carolina state\nA: SELECT Runner-Up FROM 1-11214772-1 WHERE Year = '2004' AND Champion = 'North Carolina State'"}
{"text": "table: 1-11214772-1\ncolumns: Year, Champion, Score, Runner-Up, Location, Semi-Finalist #1, Semi-Finalist #2\nQ:  who is the\u00a0runner-up\u00a0where\u00a0location\u00a0is ellenton, fl and\u00a0year\u00a0is 2004\nA: SELECT Runner-Up FROM 1-11214772-1 WHERE Location = 'Ellenton, FL' AND Year = '2004'"}
{"text": "table: 1-11214212-1\ncolumns: Year, Numer of Jamaicans granted British citizenship, Naturalisation by residence, Naturalisation by marriage, Registration of a minor child, Registration by other means\nQ: what's the\u00a0naturalisation  by marriage\u00a0with\u00a0numer of jamaicans granted british citizenship\u00a0being 3165\nA: SELECT Naturalisation by marriage FROM 1-11214212-1 WHERE Numer of Jamaicans granted British citizenship = 3165"}
{"text": "table: 1-11214212-1\ncolumns: Year, Numer of Jamaicans granted British citizenship, Naturalisation by residence, Naturalisation by marriage, Registration of a minor child, Registration by other means\nQ:  how many\u00a0numer of jamaicans granted british citizenship\u00a0with\u00a0naturalisation  by marriage\u00a0being 1060\nA: SELECT COUNT Numer of Jamaicans granted British citizenship FROM 1-11214212-1 WHERE Naturalisation by marriage = 1060"}
{"text": "table: 1-11214212-1\ncolumns: Year, Numer of Jamaicans granted British citizenship, Naturalisation by residence, Naturalisation by marriage, Registration of a minor child, Registration by other means\nQ: what's the\u00a0naturalisation by marriage\u00a0with\u00a0regbeingtration of a minor child\u00a0being 114\nA: SELECT Naturalisation by marriage FROM 1-11214212-1 WHERE Registration of a minor child = 114"}
{"text": "table: 1-11214212-1\ncolumns: Year, Numer of Jamaicans granted British citizenship, Naturalisation by residence, Naturalisation by marriage, Registration of a minor child, Registration by other means\nQ: what's the\u00a0numer of jamaicans granted british  citizenship\u00a0with\u00a0naturalisation by residence\u00a0being 927\nA: SELECT Numer of Jamaicans granted British citizenship FROM 1-11214212-1 WHERE Naturalisation by residence = 927"}
{"text": "table: 1-11214212-1\ncolumns: Year, Numer of Jamaicans granted British citizenship, Naturalisation by residence, Naturalisation by marriage, Registration of a minor child, Registration by other means\nQ: what is the maximum\u00a0year\u00a0with\u00a0registration of a minor child\u00a0being 281\nA: SELECT MAX Year FROM 1-11214212-1 WHERE Registration of a minor child = 281"}
{"text": "table: 1-11220799-2\ncolumns: Episode Titles, First air date, Reward, Immunity, Exiled, Eliminated, Vote, Finish\nQ: How many episodes had their first air date on March 6, 2008?\nA: SELECT COUNT Episode Titles FROM 1-11220799-2 WHERE First air date = 'March 6, 2008'"}
{"text": "table: 1-11220799-2\ncolumns: Episode Titles, First air date, Reward, Immunity, Exiled, Eliminated, Vote, Finish\nQ: What were the results of episodes with the first air date of March 6, 2008?\nA: SELECT Finish FROM 1-11220799-2 WHERE First air date = 'March 6, 2008'"}
{"text": "table: 1-11230937-2\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: How many millions of viewers watched episode 15?\nA: SELECT U.S. viewers (millions) FROM 1-11230937-2 WHERE No. in season = 15"}
{"text": "table: 1-11230937-2\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: How many millions of viewers watched the \"Throwing Heat\" episode?\nA: SELECT U.S. viewers (millions) FROM 1-11230937-2 WHERE Title = '\"Throwing Heat\"'"}
{"text": "table: 1-11230937-2\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: How many millions of viewers watched the episode directed by Anthony Hemingway?\nA: SELECT U.S. viewers (millions) FROM 1-11230937-2 WHERE Directed by = 'Anthony Hemingway'"}
{"text": "table: 1-11222744-2\ncolumns: Year, Title, Format, Studio, Release Date, Copyright Information, Catalog Number\nQ: The Catalog number is 80809 what is the title?\nA: SELECT Title FROM 1-11222744-2 WHERE Catalog Number = '80809'"}
{"text": "table: 1-11222744-2\ncolumns: Year, Title, Format, Studio, Release Date, Copyright Information, Catalog Number\nQ: where title is beginning callanetics , what is the total of format ?\nA: SELECT COUNT Format FROM 1-11222744-2 WHERE Title = 'Beginning Callanetics'"}
{"text": "table: 1-11222744-2\ncolumns: Year, Title, Format, Studio, Release Date, Copyright Information, Catalog Number\nQ: where catalog number is 81258 , what are all the studio ?\nA: SELECT Studio FROM 1-11222744-2 WHERE Catalog Number = '81258'"}
{"text": "table: 1-11222744-2\ncolumns: Year, Title, Format, Studio, Release Date, Copyright Information, Catalog Number\nQ: where title is am/pm callanetics , what are all the copyright information?\nA: SELECT Copyright Information FROM 1-11222744-2 WHERE Title = 'AM/PM Callanetics'"}
{"text": "table: 1-11236195-2\ncolumns: Season, Grand FinalDate, WinningTeam, Score, LosingTeam, Location, GF Attendance, Clive Churchill Medal\nQ: What was the GF attendance at the location of Sydney Football Stadium, Sydney (6)?\nA: SELECT COUNT GF Attendance FROM 1-11236195-2 WHERE Location = 'Sydney Football Stadium, Sydney (6)'"}
{"text": "table: 1-11236195-2\ncolumns: Season, Grand FinalDate, WinningTeam, Score, LosingTeam, Location, GF Attendance, Clive Churchill Medal\nQ: Which losing team had a score of 24-12?\nA: SELECT LosingTeam FROM 1-11236195-2 WHERE Score = '24-12'"}
{"text": "table: 1-11236195-2\ncolumns: Season, Grand FinalDate, WinningTeam, Score, LosingTeam, Location, GF Attendance, Clive Churchill Medal\nQ: What was the losing team in the 1993 season?\nA: SELECT LosingTeam FROM 1-11236195-2 WHERE Season = 1993"}
{"text": "table: 1-1123802-1\ncolumns: Engine, Power, continuous, Critical altitude This is the highest altitude at which the engine can achieve its full continuous power rating. Above this altitude, power falls off with height as with a naturally aspirated engine . See Supercharger#Altitude effects for details., Power, takeoff, Compression ratio, Supercharger gear ratio, Octane rating, Dry weight\nQ: What was the compression ration when the engine was Wasp Jr. T1B2?\nA: SELECT Compression ratio FROM 1-1123802-1 WHERE Engine = 'Wasp Jr. T1B2'"}
{"text": "table: 1-1123802-1\ncolumns: Engine, Power, continuous, Critical altitude This is the highest altitude at which the engine can achieve its full continuous power rating. Above this altitude, power falls off with height as with a naturally aspirated engine . See Supercharger#Altitude effects for details., Power, takeoff, Compression ratio, Supercharger gear ratio, Octane rating, Dry weight\nQ: What is the compression ration when the continuous power is hp (kw) at 2,200 RPM and the octane rating is 80/87?\nA: SELECT Compression ratio FROM 1-1123802-1 WHERE Power, continuous = 'hp (kW) at 2,200 RPM' AND Octane rating = '80/87'"}
{"text": "table: 1-1123802-1\ncolumns: Engine, Power, continuous, Critical altitude This is the highest altitude at which the engine can achieve its full continuous power rating. Above this altitude, power falls off with height as with a naturally aspirated engine . See Supercharger#Altitude effects for details., Power, takeoff, Compression ratio, Supercharger gear ratio, Octane rating, Dry weight\nQ: What is the compression ratio when the continuous power is  hp (KW) at 2,200 RPM and the critical altitude is at sea level?\nA: SELECT COUNT Compression ratio FROM 1-1123802-1 WHERE Power, continuous = 'hp (kW) at 2,200 RPM' AND Critical altitude This is the highest altitude at which the engine can achieve its full continuous power rating. Above this altitude, power falls off with height as with a naturally aspirated engine . See Supercharger#Altitude effects for details. = 'sea level'"}
{"text": "table: 1-1123802-1\ncolumns: Engine, Power, continuous, Critical altitude This is the highest altitude at which the engine can achieve its full continuous power rating. Above this altitude, power falls off with height as with a naturally aspirated engine . See Supercharger#Altitude effects for details., Power, takeoff, Compression ratio, Supercharger gear ratio, Octane rating, Dry weight\nQ: When the engine is Wasp Jr. T1B2, what is the number needed for takeoff power?\nA: SELECT COUNT Power, takeoff FROM 1-1123802-1 WHERE Engine = 'Wasp Jr. T1B2'"}
{"text": "table: 1-1123802-1\ncolumns: Engine, Power, continuous, Critical altitude This is the highest altitude at which the engine can achieve its full continuous power rating. Above this altitude, power falls off with height as with a naturally aspirated engine . See Supercharger#Altitude effects for details., Power, takeoff, Compression ratio, Supercharger gear ratio, Octane rating, Dry weight\nQ: When critical altitude is sea level, what is the compression ration for a supercharger gear ratio of 7:1?\nA: SELECT Compression ratio FROM 1-1123802-1 WHERE Critical altitude This is the highest altitude at which the engine can achieve its full continuous power rating. Above this altitude, power falls off with height as with a naturally aspirated engine . See Supercharger#Altitude effects for details. = 'sea level' AND Supercharger gear ratio = '7:1'"}
{"text": "table: 1-11235334-2\ncolumns: #, Episode, Air Date, Timeslot, Viewers, Weekly Rank for Living\nQ: How many episodes aired on october 27, 2008\nA: SELECT COUNT Episode FROM 1-11235334-2 WHERE Air Date = 'October 27, 2008'"}
{"text": "table: 1-11235334-2\ncolumns: #, Episode, Air Date, Timeslot, Viewers, Weekly Rank for Living\nQ: The episode \"chapter five: dressed to kill\" had a weekly ranking of what?\nA: SELECT Weekly Rank for Living FROM 1-11235334-2 WHERE Episode = '\"Chapter Five: Dressed to Kill\"'"}
{"text": "table: 1-11235334-2\ncolumns: #, Episode, Air Date, Timeslot, Viewers, Weekly Rank for Living\nQ: what is the most # that aired on september 29, 2008?\nA: SELECT MAX # FROM 1-11235334-2 WHERE Air Date = 'September 29, 2008'"}
{"text": "table: 1-11236195-5\ncolumns: Season, Grand FinalDate, WinningTeam, Score, LosingTeam, Location, GF Attendance, Clive Churchill Medal\nQ: How many seasons did the canterbury bulldogs (8) win?\nA: SELECT COUNT Season FROM 1-11236195-5 WHERE WinningTeam = 'Canterbury Bulldogs (8)'"}
{"text": "table: 1-11236195-5\ncolumns: Season, Grand FinalDate, WinningTeam, Score, LosingTeam, Location, GF Attendance, Clive Churchill Medal\nQ: How many teams lost at the sydney football stadium, sydney (11)?\nA: SELECT COUNT LosingTeam FROM 1-11236195-5 WHERE Location = 'Sydney Football Stadium, Sydney (11)'"}
{"text": "table: 1-11236195-5\ncolumns: Season, Grand FinalDate, WinningTeam, Score, LosingTeam, Location, GF Attendance, Clive Churchill Medal\nQ: What was the date that the st. george-illawarra dragons lost?\nA: SELECT Grand FinalDate FROM 1-11236195-5 WHERE LosingTeam = 'St. George-Illawarra Dragons'"}
{"text": "table: 1-11236195-5\ncolumns: Season, Grand FinalDate, WinningTeam, Score, LosingTeam, Location, GF Attendance, Clive Churchill Medal\nQ: Brett kimmorley, who was chosen for the clive churchill medal belonged to what team?\nA: SELECT WinningTeam FROM 1-11236195-5 WHERE Clive Churchill Medal = 'Brett Kimmorley'"}
{"text": "table: 1-11244302-1\ncolumns: #, Episode, Air Date, Time slot (EST), Rating, Share, 18-49 (Rating/Share), Viewers (m), Rank (Overall)\nQ: What time slots have a 6.3 rating\nA: SELECT Time slot (EST) FROM 1-11244302-1 WHERE Rating = '6.3'"}
{"text": "table: 1-11244302-1\ncolumns: #, Episode, Air Date, Time slot (EST), Rating, Share, 18-49 (Rating/Share), Viewers (m), Rank (Overall)\nQ: What time slot is the episode \"the way we weren't\" in\nA: SELECT Time slot (EST) FROM 1-11244302-1 WHERE Episode = '\"The Way We Weren't\"'"}
{"text": "table: 1-11244302-1\ncolumns: #, Episode, Air Date, Time slot (EST), Rating, Share, 18-49 (Rating/Share), Viewers (m), Rank (Overall)\nQ: What time slot is the episode \"who's your daddy\" in\nA: SELECT Time slot (EST) FROM 1-11244302-1 WHERE Episode = '\"Who's Your Daddy\"'"}
{"text": "table: 1-11244302-1\ncolumns: #, Episode, Air Date, Time slot (EST), Rating, Share, 18-49 (Rating/Share), Viewers (m), Rank (Overall)\nQ: Which air date had an 11 share\nA: SELECT Air Date FROM 1-11244302-1 WHERE Share = 11"}
{"text": "table: 1-11244302-1\ncolumns: #, Episode, Air Date, Time slot (EST), Rating, Share, 18-49 (Rating/Share), Viewers (m), Rank (Overall)\nQ: Which air date had the 18-49 rating/share of 3.3/9\nA: SELECT Air Date FROM 1-11244302-1 WHERE 18-49 (Rating/Share) = '3.3/9'"}
{"text": "table: 1-11240028-3\ncolumns: Character, Portrayed by, Relationship, First appearance, Last appearance\nQ: Which characters had their first experience in the episode \"consequences\"?\nA: SELECT Character FROM 1-11240028-3 WHERE First appearance = '\"Consequences\"'"}
{"text": "table: 1-11240028-3\ncolumns: Character, Portrayed by, Relationship, First appearance, Last appearance\nQ: What episode had the last appearances of the late wife of mac taylor?\nA: SELECT Last appearance FROM 1-11240028-3 WHERE Relationship = 'Late wife of Mac Taylor'"}
{"text": "table: 1-11240028-3\ncolumns: Character, Portrayed by, Relationship, First appearance, Last appearance\nQ: Which characters were portrayed by reed garrett?\nA: SELECT Portrayed by FROM 1-11240028-3 WHERE Character = 'Reed Garrett'"}
{"text": "table: 1-11240028-3\ncolumns: Character, Portrayed by, Relationship, First appearance, Last appearance\nQ: How many characters were portrayed by the informant of don flack?\nA: SELECT COUNT Portrayed by FROM 1-11240028-3 WHERE Relationship = 'Informant of Don Flack'"}
{"text": "table: 1-11240028-3\ncolumns: Character, Portrayed by, Relationship, First appearance, Last appearance\nQ: What episode was the last appearance of the character, rikki sandoval?\nA: SELECT Last appearance FROM 1-11240028-3 WHERE Character = 'Rikki Sandoval'"}
{"text": "table: 1-11240028-1\ncolumns: Character, Portrayed by, First appearance, Last appearance, Duration, Episodes\nQ: On which episode did actress Sela Ward make her last appearance?\nA: SELECT Last appearance FROM 1-11240028-1 WHERE Portrayed by = 'Sela Ward'"}
{"text": "table: 1-11240028-1\ncolumns: Character, Portrayed by, First appearance, Last appearance, Duration, Episodes\nQ: Which actors first appeared in \"Zoo York\"?\nA: SELECT Portrayed by FROM 1-11240028-1 WHERE First appearance = '\"Zoo York\"'"}
{"text": "table: 1-11240028-1\ncolumns: Character, Portrayed by, First appearance, Last appearance, Duration, Episodes\nQ: How many episodes did actress Vanessa Ferlito appear in?\nA: SELECT Episodes FROM 1-11240028-1 WHERE Portrayed by = 'Vanessa Ferlito'"}
{"text": "table: 1-11240028-1\ncolumns: Character, Portrayed by, First appearance, Last appearance, Duration, Episodes\nQ: Which actors first appeared in episode \"Blink\" 1, 2, 3?\nA: SELECT Portrayed by FROM 1-11240028-1 WHERE First appearance = '\"Blink\" 1, 2, 3'"}
{"text": "table: 1-11240028-1\ncolumns: Character, Portrayed by, First appearance, Last appearance, Duration, Episodes\nQ: What was the duration of Robert Joy's portrayal?\nA: SELECT COUNT Duration FROM 1-11240028-1 WHERE Portrayed by = 'Robert Joy'"}
{"text": "table: 1-11240028-1\ncolumns: Character, Portrayed by, First appearance, Last appearance, Duration, Episodes\nQ: Which episode did actor A. J. Buckley last appear in?\nA: SELECT Last appearance FROM 1-11240028-1 WHERE Portrayed by = 'A. J. Buckley'"}
{"text": "table: 1-11250-4\ncolumns: Club, Position in 2012\u201313, First season in top division, Number of seasons in top division, Number of seasons in the Premier League, First season of current spell in top division, Top division titles, Last top division title\nQ: What is the least top division titles?\nA: SELECT MIN Top division titles FROM 1-11250-4"}
{"text": "table: 1-11250-4\ncolumns: Club, Position in 2012\u201313, First season in top division, Number of seasons in top division, Number of seasons in the Premier League, First season of current spell in top division, Top division titles, Last top division title\nQ: What is the least number of seasons in top division?\nA: SELECT MIN Number of seasons in top division FROM 1-11250-4"}
{"text": "table: 1-11253290-2\ncolumns: #, Episode, Rating, Share, Rating/Share (18-49), Viewers (millions), Rank (timeslot), Rank (night), Rank (week)\nQ: How many viewers (millions) were there for rank (week) 20?\nA: SELECT COUNT Viewers (millions) FROM 1-11253290-2 WHERE Rank (week) = '20'"}
{"text": "table: 1-11253290-2\ncolumns: #, Episode, Rating, Share, Rating/Share (18-49), Viewers (millions), Rank (timeslot), Rank (night), Rank (week)\nQ: What is the rank (timeslot) with the episode name \"dangerous liaisons\"?\nA: SELECT Rank (timeslot) FROM 1-11253290-2 WHERE Episode = '\"Dangerous Liaisons\"'"}
{"text": "table: 1-11253290-2\ncolumns: #, Episode, Rating, Share, Rating/Share (18-49), Viewers (millions), Rank (timeslot), Rank (night), Rank (week)\nQ: What is the lowest rank (night) for having viewers (millions) 5.25?\nA: SELECT MIN Rank (night) FROM 1-11253290-2 WHERE Viewers (millions) = '5.25'"}
{"text": "table: 1-11253290-2\ncolumns: #, Episode, Rating, Share, Rating/Share (18-49), Viewers (millions), Rank (timeslot), Rank (night), Rank (week)\nQ: How many times was the episode named \"conference call\"?\nA: SELECT COUNT # FROM 1-11253290-2 WHERE Episode = '\"Conference Call\"'"}
{"text": "table: 1-11253290-2\ncolumns: #, Episode, Rating, Share, Rating/Share (18-49), Viewers (millions), Rank (timeslot), Rank (night), Rank (week)\nQ: How many times was the rank (night) 11?\nA: SELECT COUNT Viewers (millions) FROM 1-11253290-2 WHERE Rank (night) = 11"}
{"text": "table: 1-11251601-2\ncolumns: Country, Carbon dioxide emissions per year (10 6 Tons) (2006), Percentage of global total, Avg. emission per km 2 of its land (tons), Carbon dioxide emissions per year (Tons per person) (2007)\nQ: WHAT WAS THE AMOUNT OF CARBON DIOXIDE EMISSIONS  IN 2006 IN THE COUNTRY WHOSE  CO2 EMISSIONS (TONS PER PERSON)  REACHED 1.4 IN 2OO7?\nA: SELECT Carbon dioxide emissions per year (10 6 Tons) (2006) FROM 1-11251601-2 WHERE Carbon dioxide emissions per year (Tons per person) (2007) = '1.4'"}
{"text": "table: 1-11251601-2\ncolumns: Country, Carbon dioxide emissions per year (10 6 Tons) (2006), Percentage of global total, Avg. emission per km 2 of its land (tons), Carbon dioxide emissions per year (Tons per person) (2007)\nQ: HOW MANY TONS OF CO2 EMISSIONS DID RUSSIA PRODUCE IN 2006?\nA: SELECT MAX Carbon dioxide emissions per year (10 6 Tons) (2006) FROM 1-11251601-2 WHERE Country = 'Russia'"}
{"text": "table: 1-11251601-2\ncolumns: Country, Carbon dioxide emissions per year (10 6 Tons) (2006), Percentage of global total, Avg. emission per km 2 of its land (tons), Carbon dioxide emissions per year (Tons per person) (2007)\nQ: WHAT PERCENTAGE OF GLOBAL TOTAL EMISSIONS DID INDIA PRODUCE?\nA: SELECT Percentage of global total FROM 1-11251601-2 WHERE Country = 'India'"}
{"text": "table: 1-11251601-2\ncolumns: Country, Carbon dioxide emissions per year (10 6 Tons) (2006), Percentage of global total, Avg. emission per km 2 of its land (tons), Carbon dioxide emissions per year (Tons per person) (2007)\nQ: HOW MUCH IS THE PERCENTAGE OF GLOBAL TOTAL EMISSIONS IN THE COUNTRY THAT PRODUCED 4.9 TONS PER PERSON IN 2007?\nA: SELECT Percentage of global total FROM 1-11251601-2 WHERE Carbon dioxide emissions per year (Tons per person) (2007) = '4.9'"}
{"text": "table: 1-11251601-2\ncolumns: Country, Carbon dioxide emissions per year (10 6 Tons) (2006), Percentage of global total, Avg. emission per km 2 of its land (tons), Carbon dioxide emissions per year (Tons per person) (2007)\nQ: WHAT WAS THE AVERAGE EMISSION PER KM 2 IN INDIA?\nA: SELECT MAX Avg. emission per km 2 of its land (tons) FROM 1-11251601-2 WHERE Country = 'India'"}
{"text": "table: 1-11251109-3\ncolumns: #, Episode, Air Date, Timeslot (EST), Season, Rating, Share, 18\u201349, Viewers (m), Rank (#)\nQ: What is the rank number that aired october 26, 2007?\nA: SELECT Rank (#) FROM 1-11251109-3 WHERE Air Date = 'October 26, 2007'"}
{"text": "table: 1-11251109-3\ncolumns: #, Episode, Air Date, Timeslot (EST), Season, Rating, Share, 18\u201349, Viewers (m), Rank (#)\nQ: What is the number of rank with the viewership of 5.96 million?\nA: SELECT COUNT Rank (#) FROM 1-11251109-3 WHERE Viewers (m) = '5.96'"}
{"text": "table: 1-11251109-3\ncolumns: #, Episode, Air Date, Timeslot (EST), Season, Rating, Share, 18\u201349, Viewers (m), Rank (#)\nQ: What is the viewership on november 9, 2007?\nA: SELECT Viewers (m) FROM 1-11251109-3 WHERE Air Date = 'November 9, 2007'"}
{"text": "table: 1-11254821-2\ncolumns: Finishing position, Points awarded (Platinum), Points awarded (Gold), Points awarded (Silver), Points awarded (Satellite)\nQ: How many platinum points were awarded when 6 gold points were awarded?\nA: SELECT MAX Points awarded (Platinum) FROM 1-11254821-2 WHERE Points awarded (Gold) = 6"}
{"text": "table: 1-11254821-2\ncolumns: Finishing position, Points awarded (Platinum), Points awarded (Gold), Points awarded (Silver), Points awarded (Satellite)\nQ: What was the range of finishing position for 15 awarded platinum points?\nA: SELECT Finishing position FROM 1-11254821-2 WHERE Points awarded (Platinum) = 15"}
{"text": "table: 1-11254821-2\ncolumns: Finishing position, Points awarded (Platinum), Points awarded (Gold), Points awarded (Silver), Points awarded (Satellite)\nQ: How many platinum points were awarded for 5th place?\nA: SELECT MAX Points awarded (Platinum) FROM 1-11254821-2 WHERE Finishing position = '5th'"}
{"text": "table: 1-11254821-2\ncolumns: Finishing position, Points awarded (Platinum), Points awarded (Gold), Points awarded (Silver), Points awarded (Satellite)\nQ: How many platinum points were awarded when 70 silver points were awarded?\nA: SELECT Points awarded (Platinum) FROM 1-11254821-2 WHERE Points awarded (Silver) = 70"}
{"text": "table: 1-11254821-2\ncolumns: Finishing position, Points awarded (Platinum), Points awarded (Gold), Points awarded (Silver), Points awarded (Satellite)\nQ: How many platinum points were awarded when 9 gold points were awarded?\nA: SELECT Points awarded (Platinum) FROM 1-11254821-2 WHERE Points awarded (Gold) = 9"}
{"text": "table: 1-11274401-2\ncolumns: No., Episode, Air Date, Timeslot, Rating, Share, 18\u201349 (Rating/Share), Viewers (m), Rank (#)\nQ: How did the episode rank that had 2.65 million viewers?\nA: SELECT Rank (#) FROM 1-11274401-2 WHERE Viewers (m) = '2.65'"}
{"text": "table: 1-11274401-2\ncolumns: No., Episode, Air Date, Timeslot, Rating, Share, 18\u201349 (Rating/Share), Viewers (m), Rank (#)\nQ: What was the share for the first episode that ranked 85?\nA: SELECT MIN Share FROM 1-11274401-2 WHERE Rank (#) = '85'"}
{"text": "table: 1-11274401-2\ncolumns: No., Episode, Air Date, Timeslot, Rating, Share, 18\u201349 (Rating/Share), Viewers (m), Rank (#)\nQ: Which timeslot did episode no. 15 hold?\nA: SELECT Timeslot FROM 1-11274401-2 WHERE No. = 15"}
{"text": "table: 1-11274401-3\ncolumns: No., Episode, Air Date, Timeslot, Rating, Share, 18\u201349 (Rating/Share), Viewers (m), Rank (#)\nQ: What was the timeslot for the episode that aired on May 12, 2009?\nA: SELECT Timeslot FROM 1-11274401-3 WHERE Air Date = 'May 12, 2009'"}
{"text": "table: 1-11274401-3\ncolumns: No., Episode, Air Date, Timeslot, Rating, Share, 18\u201349 (Rating/Share), Viewers (m), Rank (#)\nQ: What's the 18-49 (rating/share) of the episode that originally aired on May 5, 2009?\nA: SELECT 18\u201349 (Rating/Share) FROM 1-11274401-3 WHERE Air Date = 'May 5, 2009'"}
{"text": "table: 1-11274401-3\ncolumns: No., Episode, Air Date, Timeslot, Rating, Share, 18\u201349 (Rating/Share), Viewers (m), Rank (#)\nQ: What's the total number of episodes whose original airings were viewed by 1.82 million viewers?\nA: SELECT COUNT Air Date FROM 1-11274401-3 WHERE Viewers (m) = '1.82'"}
{"text": "table: 1-11274401-3\ncolumns: No., Episode, Air Date, Timeslot, Rating, Share, 18\u201349 (Rating/Share), Viewers (m), Rank (#)\nQ: What's the rating of the episode originally aired on May 5, 2009?\nA: SELECT Rating FROM 1-11274401-3 WHERE Air Date = 'May 5, 2009'"}
{"text": "table: 1-11274401-3\ncolumns: No., Episode, Air Date, Timeslot, Rating, Share, 18\u201349 (Rating/Share), Viewers (m), Rank (#)\nQ: What episode was seen by 2.05 million viewers?\nA: SELECT Episode FROM 1-11274401-3 WHERE Viewers (m) = '2.05'"}
{"text": "table: 1-11256021-1\ncolumns: Date, Founder, Extroversion Scales, People-task orientation scale, Introverted, Task-Oriented, Extroverted, Task-Oriented, Extroverted, Relationship-Oriented, Introverted, Relationship Oriented, Moderate\nQ:  what's the\u00a0extroverted, relationship-oriented\u00a0where\u00a0extroverted, task-oriented\u00a0is director\nA: SELECT Extroverted, Relationship-Oriented FROM 1-11256021-1 WHERE Extroverted, Task-Oriented = 'Director'"}
{"text": "table: 1-11256021-1\ncolumns: Date, Founder, Extroversion Scales, People-task orientation scale, Introverted, Task-Oriented, Extroverted, Task-Oriented, Extroverted, Relationship-Oriented, Introverted, Relationship Oriented, Moderate\nQ:  what's the\u00a0extroverted, relationship-oriented\u00a0where\u00a0moderate\u00a0is introverted sanguine\nA: SELECT Extroverted, Relationship-Oriented FROM 1-11256021-1 WHERE Moderate = 'Introverted Sanguine'"}
{"text": "table: 1-11256021-1\ncolumns: Date, Founder, Extroversion Scales, People-task orientation scale, Introverted, Task-Oriented, Extroverted, Task-Oriented, Extroverted, Relationship-Oriented, Introverted, Relationship Oriented, Moderate\nQ:  what's the\u00a0founder\u00a0where\u00a0moderate\u00a0is ether\nA: SELECT Founder FROM 1-11256021-1 WHERE Moderate = 'ether'"}
{"text": "table: 1-11256021-1\ncolumns: Date, Founder, Extroversion Scales, People-task orientation scale, Introverted, Task-Oriented, Extroverted, Task-Oriented, Extroverted, Relationship-Oriented, Introverted, Relationship Oriented, Moderate\nQ:  what's the\u00a0extroverted, relationship-oriented\u00a0where\u00a0date\u00a0is c. 1928\nA: SELECT Extroverted, Relationship-Oriented FROM 1-11256021-1 WHERE Date = 'c. 1928'"}
{"text": "table: 1-11256021-1\ncolumns: Date, Founder, Extroversion Scales, People-task orientation scale, Introverted, Task-Oriented, Extroverted, Task-Oriented, Extroverted, Relationship-Oriented, Introverted, Relationship Oriented, Moderate\nQ:  who is the\u00a0founder\u00a0where\u00a0date\u00a0is c. 1900\nA: SELECT Founder FROM 1-11256021-1 WHERE Date = 'c. 1900'"}
{"text": "table: 1-11256021-1\ncolumns: Date, Founder, Extroversion Scales, People-task orientation scale, Introverted, Task-Oriented, Extroverted, Task-Oriented, Extroverted, Relationship-Oriented, Introverted, Relationship Oriented, Moderate\nQ:  what's the\u00a0people-task orientation scale\u00a0where\u00a0extroverted, relationship-oriented\u00a0is team type\nA: SELECT People-task orientation scale FROM 1-11256021-1 WHERE Extroverted, Relationship-Oriented = 'Team Type'"}
{"text": "table: 1-11303072-5\ncolumns: Wicket, Runs, Batting partners, Batting team, Fielding team, Venue, Season\nQ: What is the batting team where the runs are 276?\nA: SELECT Batting team FROM 1-11303072-5 WHERE Runs = '276'"}
{"text": "table: 1-11303072-5\ncolumns: Wicket, Runs, Batting partners, Batting team, Fielding team, Venue, Season\nQ: Name the batting team at Durham\nA: SELECT Batting team FROM 1-11303072-5 WHERE Fielding team = 'Durham'"}
{"text": "table: 1-11303072-5\ncolumns: Wicket, Runs, Batting partners, Batting team, Fielding team, Venue, Season\nQ: What is the batting team with the batting partnets of thilina kandamby and rangana herath?\nA: SELECT Batting team FROM 1-11303072-5 WHERE Batting partners = 'Thilina Kandamby and Rangana Herath'"}
{"text": "table: 1-11303072-5\ncolumns: Wicket, Runs, Batting partners, Batting team, Fielding team, Venue, Season\nQ: What is the fielding team with 155 runs?\nA: SELECT Fielding team FROM 1-11303072-5 WHERE Runs = '155'"}
{"text": "table: 1-11303072-5\ncolumns: Wicket, Runs, Batting partners, Batting team, Fielding team, Venue, Season\nQ: What is the batting partners with runs of 226?\nA: SELECT Batting partners FROM 1-11303072-5 WHERE Runs = '226'"}
{"text": "table: 1-11303072-9\ncolumns: Rank, Dismissals, Player, Nationality, Catches, Stumpings, Career Span\nQ: What is the nationality of David Bairstow?\nA: SELECT Nationality FROM 1-11303072-9 WHERE Player = 'David Bairstow'"}
{"text": "table: 1-11303072-9\ncolumns: Rank, Dismissals, Player, Nationality, Catches, Stumpings, Career Span\nQ: What are the players whose rank is 2?\nA: SELECT Player FROM 1-11303072-9 WHERE Rank = 2"}
{"text": "table: 1-11303072-9\ncolumns: Rank, Dismissals, Player, Nationality, Catches, Stumpings, Career Span\nQ: How many stumpings has Paul Nixon in his career?\nA: SELECT Stumpings FROM 1-11303072-9 WHERE Player = 'Paul Nixon'"}
{"text": "table: 1-11303072-9\ncolumns: Rank, Dismissals, Player, Nationality, Catches, Stumpings, Career Span\nQ: Where is Adam Gilchrist from?\nA: SELECT Nationality FROM 1-11303072-9 WHERE Player = 'Adam Gilchrist'"}
{"text": "table: 1-1130632-1\ncolumns: No. in series, Title, Directed by, Written by, Featured character(s), Original air date, U.S. viewers (million)\nQ: What are the title that have 19.48 million u.s. viewers?\nA: SELECT Title FROM 1-1130632-1 WHERE U.S. viewers (million) = '19.48'"}
{"text": "table: 1-1130632-1\ncolumns: No. in series, Title, Directed by, Written by, Featured character(s), Original air date, U.S. viewers (million)\nQ: Which titles have 18.73 u.s. viewers.\nA: SELECT Title FROM 1-1130632-1 WHERE U.S. viewers (million) = '18.73'"}
{"text": "table: 1-1130632-1\ncolumns: No. in series, Title, Directed by, Written by, Featured character(s), Original air date, U.S. viewers (million)\nQ: Who wrote all the shows with 18.73 u.s. viewers?\nA: SELECT Written by FROM 1-1130632-1 WHERE U.S. viewers (million) = '18.73'"}
{"text": "table: 1-1131183-2\ncolumns: Rank ( WJC ), Rank (ARDA), Metro area, Number of Jews (WJC), Number of Jews (ASARB)\nQ: What is the rank where the area is Los Angeles?\nA: SELECT Rank ( WJC ) FROM 1-1131183-2 WHERE Metro area = 'Los Angeles'"}
{"text": "table: 1-1131183-2\ncolumns: Rank ( WJC ), Rank (ARDA), Metro area, Number of Jews (WJC), Number of Jews (ASARB)\nQ: What is the number of jews where the rank is 1?\nA: SELECT COUNT Number of Jews (WJC) FROM 1-1131183-2 WHERE Rank (ARDA) = 1"}
{"text": "table: 1-1131183-2\ncolumns: Rank ( WJC ), Rank (ARDA), Metro area, Number of Jews (WJC), Number of Jews (ASARB)\nQ: What is the number of jews asarb where the metro area is philadelphia?\nA: SELECT Number of Jews (ASARB) FROM 1-1131183-2 WHERE Metro area = 'Philadelphia'"}
{"text": "table: 1-11318462-5\ncolumns: Crew, Open 1st VIII, Open 2nd VIII, Open 3rd VIII, U16 1st VIII, U16 2nd VIII, U16 3rd VIII, U15 1st IV, U15 2nd IV, U15 3rd IV, U15 4th IV, U15 5th IV, U15 6th IV\nQ: what are all the open 1st viii with u15 6th iv being bgs\nA: SELECT Open 1st VIII FROM 1-11318462-5 WHERE U15 6th IV = 'BGS'"}
{"text": "table: 1-11318462-5\ncolumns: Crew, Open 1st VIII, Open 2nd VIII, Open 3rd VIII, U16 1st VIII, U16 2nd VIII, U16 3rd VIII, U15 1st IV, U15 2nd IV, U15 3rd IV, U15 4th IV, U15 5th IV, U15 6th IV\nQ: what are all the u16 2nd viii with u15 3rd iv being bbc\nA: SELECT U16 2nd VIII FROM 1-11318462-5 WHERE U15 3rd IV = 'BBC'"}
{"text": "table: 1-11318462-5\ncolumns: Crew, Open 1st VIII, Open 2nd VIII, Open 3rd VIII, U16 1st VIII, U16 2nd VIII, U16 3rd VIII, U15 1st IV, U15 2nd IV, U15 3rd IV, U15 4th IV, U15 5th IV, U15 6th IV\nQ: what are all the open 1st viii with u15 4th iv being gt\nA: SELECT Open 1st VIII FROM 1-11318462-5 WHERE U15 4th IV = 'GT'"}
{"text": "table: 1-11318462-5\ncolumns: Crew, Open 1st VIII, Open 2nd VIII, Open 3rd VIII, U16 1st VIII, U16 2nd VIII, U16 3rd VIII, U15 1st IV, U15 2nd IV, U15 3rd IV, U15 4th IV, U15 5th IV, U15 6th IV\nQ: how many crew had u15 3rd iv being bgs and u15 1st iv being acgs and open 1st viii being acgs\nA: SELECT COUNT Crew FROM 1-11318462-5 WHERE U15 3rd IV = 'BGS' AND U15 1st IV = 'ACGS' AND Open 1st VIII = 'ACGS'"}
{"text": "table: 1-11318462-5\ncolumns: Crew, Open 1st VIII, Open 2nd VIII, Open 3rd VIII, U16 1st VIII, U16 2nd VIII, U16 3rd VIII, U15 1st IV, U15 2nd IV, U15 3rd IV, U15 4th IV, U15 5th IV, U15 6th IV\nQ: what are all the u15 3rd iv with u15 4th iv being bbc\nA: SELECT U15 3rd IV FROM 1-11318462-5 WHERE U15 4th IV = 'BBC'"}
{"text": "table: 1-11318462-5\ncolumns: Crew, Open 1st VIII, Open 2nd VIII, Open 3rd VIII, U16 1st VIII, U16 2nd VIII, U16 3rd VIII, U15 1st IV, U15 2nd IV, U15 3rd IV, U15 4th IV, U15 5th IV, U15 6th IV\nQ: how many open 2nd viii had u15 3rd iv being gt\nA: SELECT COUNT Open 2nd VIII FROM 1-11318462-5 WHERE U15 3rd IV = 'GT'"}
{"text": "table: 1-11318462-29\ncolumns: School, Location, Enrolment, Founded, Denomination, Day/Boarding, School Colours, Abbreviation, In competition since\nQ: How many schools have an enrollment of 850?\nA: SELECT COUNT Founded FROM 1-11318462-29 WHERE Enrolment = 850"}
{"text": "table: 1-11318462-29\ncolumns: School, Location, Enrolment, Founded, Denomination, Day/Boarding, School Colours, Abbreviation, In competition since\nQ: What is the location of the school named Brisbane Girls' Grammar School?\nA: SELECT Location FROM 1-11318462-29 WHERE School = 'Brisbane Girls' Grammar School'"}
{"text": "table: 1-11318462-29\ncolumns: School, Location, Enrolment, Founded, Denomination, Day/Boarding, School Colours, Abbreviation, In competition since\nQ: How many schools are located in South Brisbane?\nA: SELECT COUNT School FROM 1-11318462-29 WHERE Location = 'South Brisbane'"}
{"text": "table: 1-11318462-29\ncolumns: School, Location, Enrolment, Founded, Denomination, Day/Boarding, School Colours, Abbreviation, In competition since\nQ: When was SPLC founded?\nA: SELECT MIN Founded FROM 1-11318462-29 WHERE Abbreviation = 'SPLC'"}
{"text": "table: 1-11318462-29\ncolumns: School, Location, Enrolment, Founded, Denomination, Day/Boarding, School Colours, Abbreviation, In competition since\nQ: What is the enrollment of STM which has been in competition since 1990?\nA: SELECT COUNT Enrolment FROM 1-11318462-29 WHERE In competition since = 1990 AND Abbreviation = 'STM'"}
{"text": "table: 1-1132568-3\ncolumns: Rd., Grand Prix, Pole Position, Fastest Lap, Winning Driver, Constructor, Report\nQ: What number is the Monaco Grand Prix?\nA: SELECT Rd. FROM 1-1132568-3 WHERE Grand Prix = 'Monaco Grand Prix'"}
{"text": "table: 1-1132568-3\ncolumns: Rd., Grand Prix, Pole Position, Fastest Lap, Winning Driver, Constructor, Report\nQ: Who is in the pole position for the French Grand Prix?\nA: SELECT Pole Position FROM 1-1132568-3 WHERE Grand Prix = 'French Grand Prix'"}
{"text": "table: 1-1132568-3\ncolumns: Rd., Grand Prix, Pole Position, Fastest Lap, Winning Driver, Constructor, Report\nQ: What are the numbers for the raceways that are constructed by Ferrari, with Michael Schumacher holding the fastest lap and pole position?\nA: SELECT Rd. FROM 1-1132568-3 WHERE Fastest Lap = 'Michael Schumacher' AND Constructor = 'Ferrari' AND Pole Position = 'Michael Schumacher'"}
{"text": "table: 1-1132568-3\ncolumns: Rd., Grand Prix, Pole Position, Fastest Lap, Winning Driver, Constructor, Report\nQ: How many on the list are called the Austrian Grand Prix?\nA: SELECT COUNT Rd. FROM 1-1132568-3 WHERE Grand Prix = 'Austrian Grand Prix'"}
{"text": "table: 1-1132568-3\ncolumns: Rd., Grand Prix, Pole Position, Fastest Lap, Winning Driver, Constructor, Report\nQ: What number is the Canadian Grand Prix on the list?\nA: SELECT Rd. FROM 1-1132568-3 WHERE Grand Prix = 'Canadian Grand Prix'"}
{"text": "table: 1-1132588-3\ncolumns: Rd., Grand Prix, Pole Position, Fastest Lap, Winning Driver, Constructor, Report\nQ: What is the rd for the canadian grand prix?\nA: SELECT Rd. FROM 1-1132588-3 WHERE Grand Prix = 'Canadian Grand Prix'"}
{"text": "table: 1-1132588-3\ncolumns: Rd., Grand Prix, Pole Position, Fastest Lap, Winning Driver, Constructor, Report\nQ: What is the fastest lap for the european grand prix?\nA: SELECT Fastest Lap FROM 1-1132588-3 WHERE Grand Prix = 'European Grand Prix'"}
{"text": "table: 1-1132588-3\ncolumns: Rd., Grand Prix, Pole Position, Fastest Lap, Winning Driver, Constructor, Report\nQ: What is the pole position for the ferrari at the austrian grand prix?\nA: SELECT Pole Position FROM 1-1132588-3 WHERE Constructor = 'Ferrari' AND Grand Prix = 'Austrian Grand Prix'"}
{"text": "table: 1-11326124-3\ncolumns: Edition, Zone, Round, Date, Against, Surface, Opponent, Outcome, Result\nQ: What was the result of round 2r?\nA: SELECT Outcome FROM 1-11326124-3 WHERE Round = '2R'"}
{"text": "table: 1-11326124-3\ncolumns: Edition, Zone, Round, Date, Against, Surface, Opponent, Outcome, Result\nQ: Who did Tina Pisnik verse?\nA: SELECT Against FROM 1-11326124-3 WHERE Opponent = 'Tina Pisnik'"}
{"text": "table: 1-11326124-3\ncolumns: Edition, Zone, Round, Date, Against, Surface, Opponent, Outcome, Result\nQ: How many rounds were 2r?\nA: SELECT COUNT Result FROM 1-11326124-3 WHERE Round = '2R'"}
{"text": "table: 1-11326124-3\ncolumns: Edition, Zone, Round, Date, Against, Surface, Opponent, Outcome, Result\nQ: Name the outcome for round 2r\nA: SELECT Outcome FROM 1-11326124-3 WHERE Round = '2R'"}
{"text": "table: 1-11354111-3\ncolumns: #, Episode, Air Date, Rating, Share, Rating/Share 18\u201349, Viewers (m), Timeslot Rank, Night Rank, Overall Rank\nQ: what's the night rank with viewers (m) of 6.63\nA: SELECT Night Rank FROM 1-11354111-3 WHERE Viewers (m) = '6.63'"}
{"text": "table: 1-11354111-3\ncolumns: #, Episode, Air Date, Rating, Share, Rating/Share 18\u201349, Viewers (m), Timeslot Rank, Night Rank, Overall Rank\nQ: what's the overall rank with viewers (m) of 7.44\nA: SELECT Overall Rank FROM 1-11354111-3 WHERE Viewers (m) = '7.44'"}
{"text": "table: 1-11354111-3\ncolumns: #, Episode, Air Date, Rating, Share, Rating/Share 18\u201349, Viewers (m), Timeslot Rank, Night Rank, Overall Rank\nQ: what's the overall rank with rating/share 18\u201349 of 2.1/5\nA: SELECT COUNT Overall Rank FROM 1-11354111-3 WHERE Rating/Share 18\u201349 = '2.1/5'"}
{"text": "table: 1-11354111-3\ncolumns: #, Episode, Air Date, Rating, Share, Rating/Share 18\u201349, Viewers (m), Timeslot Rank, Night Rank, Overall Rank\nQ: what's the night rank with rating of 6.2\nA: SELECT Night Rank FROM 1-11354111-3 WHERE Rating = '6.2'"}
{"text": "table: 1-11354111-3\ncolumns: #, Episode, Air Date, Rating, Share, Rating/Share 18\u201349, Viewers (m), Timeslot Rank, Night Rank, Overall Rank\nQ: what's the viewers (m) with episode of \"legacy\"\nA: SELECT Viewers (m) FROM 1-11354111-3 WHERE Episode = '\"Legacy\"'"}
{"text": "table: 1-1137142-1\ncolumns: Season, Group A Winner, Group B Winner, Group C Winner, Group D Winner\nQ: What is the number of group b winner for francavilla?\nA: SELECT COUNT Group B Winner FROM 1-1137142-1 WHERE Group C Winner = 'Francavilla'"}
{"text": "table: 1-1137142-1\ncolumns: Season, Group A Winner, Group B Winner, Group C Winner, Group D Winner\nQ: What is the group a winner for modena?\nA: SELECT Group A Winner FROM 1-1137142-1 WHERE Group B Winner = 'Modena'"}
{"text": "table: 1-1137142-1\ncolumns: Season, Group A Winner, Group B Winner, Group C Winner, Group D Winner\nQ: What is the group a winner for vis pesaro?\nA: SELECT Group A Winner FROM 1-1137142-1 WHERE Group C Winner = 'Vis Pesaro'"}
{"text": "table: 1-1137142-1\ncolumns: Season, Group A Winner, Group B Winner, Group C Winner, Group D Winner\nQ: What group a winner was for nocerina?\nA: SELECT Group A Winner FROM 1-1137142-1 WHERE Group D Winner = 'Nocerina'"}
{"text": "table: 1-1137142-1\ncolumns: Season, Group A Winner, Group B Winner, Group C Winner, Group D Winner\nQ: What was the group d winner for modena?\nA: SELECT Group D Winner FROM 1-1137142-1 WHERE Group B Winner = 'Modena'"}
{"text": "table: 1-1137695-3\ncolumns: Round, Grand Prix, Pole Position, Fastest Lap, Winning Driver, Winning Constructor, Report\nQ: Who had the fastest lap at the brazilian grand prix?\nA: SELECT Fastest Lap FROM 1-1137695-3 WHERE Grand Prix = 'Brazilian Grand Prix'"}
{"text": "table: 1-1137695-3\ncolumns: Round, Grand Prix, Pole Position, Fastest Lap, Winning Driver, Winning Constructor, Report\nQ: Who was on the pole position at the monaco grand prix?\nA: SELECT Pole Position FROM 1-1137695-3 WHERE Grand Prix = 'Monaco Grand Prix'"}
{"text": "table: 1-1137695-3\ncolumns: Round, Grand Prix, Pole Position, Fastest Lap, Winning Driver, Winning Constructor, Report\nQ: Who was the winning driver when Michael Schumacher had the pole and the fastest lap?\nA: SELECT Winning Driver FROM 1-1137695-3 WHERE Fastest Lap = 'Michael Schumacher' AND Pole Position = 'Michael Schumacher'"}
{"text": "table: 1-1137704-2\ncolumns: Round, Grand Prix, Date, Location, Pole Position, Fastest Lap, Winning Driver, Winning Constructor, Report\nQ: what are all the\u00a0location\u00a0where\u00a0date\u00a0is 5 april\nA: SELECT Location FROM 1-1137704-2 WHERE Date = '5 April'"}
{"text": "table: 1-1137704-2\ncolumns: Round, Grand Prix, Date, Location, Pole Position, Fastest Lap, Winning Driver, Winning Constructor, Report\nQ: what are all the\u00a0pole position\u00a0where\u00a0date\u00a0is 26 july\nA: SELECT Pole Position FROM 1-1137704-2 WHERE Date = '26 July'"}
{"text": "table: 1-1137704-2\ncolumns: Round, Grand Prix, Date, Location, Pole Position, Fastest Lap, Winning Driver, Winning Constructor, Report\nQ: who are all the\u00a0winning constructors\u00a0where\u00a0fastest lap\u00a0is riccardo patrese and\u00a0location\u00a0is interlagos\nA: SELECT Winning Constructor FROM 1-1137704-2 WHERE Fastest Lap = 'Riccardo Patrese' AND Location = 'Interlagos'"}
{"text": "table: 1-1137704-2\ncolumns: Round, Grand Prix, Date, Location, Pole Position, Fastest Lap, Winning Driver, Winning Constructor, Report\nQ: what are all the\u00a0report\u00a0where\u00a0winning constructor\u00a0is williams - renault and\u00a0grand prix\u00a0is south african grand prix\nA: SELECT Report FROM 1-1137704-2 WHERE Winning Constructor = 'Williams - Renault' AND Grand Prix = 'South African Grand Prix'"}
{"text": "table: 1-1137704-2\ncolumns: Round, Grand Prix, Date, Location, Pole Position, Fastest Lap, Winning Driver, Winning Constructor, Report\nQ: whatthe minimum\u00a0round\u00a0where\u00a0grand prix\u00a0is german grand prix\nA: SELECT MIN Round FROM 1-1137704-2 WHERE Grand Prix = 'German Grand Prix'"}
{"text": "table: 1-1137704-2\ncolumns: Round, Grand Prix, Date, Location, Pole Position, Fastest Lap, Winning Driver, Winning Constructor, Report\nQ: what of the total number of\u00a0date\u00a0where\u00a0grand prix\u00a0is portuguese grand prix\nA: SELECT COUNT Date FROM 1-1137704-2 WHERE Grand Prix = 'Portuguese Grand Prix'"}
{"text": "table: 1-1137707-2\ncolumns: Round, Grand Prix, Date, Location, Pole Position, Fastest Lap, Winning Driver, Winning Constructor, Report\nQ: What is the number of pole position with a round of 15?\nA: SELECT COUNT Pole Position FROM 1-1137707-2 WHERE Round = 15"}
{"text": "table: 1-1137707-2\ncolumns: Round, Grand Prix, Date, Location, Pole Position, Fastest Lap, Winning Driver, Winning Constructor, Report\nQ: What is the date of the circuit gilles villeneuve?\nA: SELECT Date FROM 1-1137707-2 WHERE Location = 'Circuit Gilles Villeneuve'"}
{"text": "table: 1-1137707-2\ncolumns: Round, Grand Prix, Date, Location, Pole Position, Fastest Lap, Winning Driver, Winning Constructor, Report\nQ: What is the location of thierry boutsen?\nA: SELECT Location FROM 1-1137707-2 WHERE Fastest Lap = 'Thierry Boutsen'"}
{"text": "table: 1-1137718-2\ncolumns: Rd., Grand Prix, Date, Location, Pole Position, Fastest Lap, Winning Driver, Constructor, Report\nQ: Who had the pole position at the German Grand Prix?\nA: SELECT Pole Position FROM 1-1137718-2 WHERE Grand Prix = 'German Grand Prix'"}
{"text": "table: 1-1137718-2\ncolumns: Rd., Grand Prix, Date, Location, Pole Position, Fastest Lap, Winning Driver, Constructor, Report\nQ: Which rd. occurred on 22 October?\nA: SELECT MIN Rd. FROM 1-1137718-2 WHERE Date = '22 October'"}
{"text": "table: 1-1137718-2\ncolumns: Rd., Grand Prix, Date, Location, Pole Position, Fastest Lap, Winning Driver, Constructor, Report\nQ: Who was the winning driver on 13 August?\nA: SELECT Winning Driver FROM 1-1137718-2 WHERE Date = '13 August'"}
{"text": "table: 1-1137718-2\ncolumns: Rd., Grand Prix, Date, Location, Pole Position, Fastest Lap, Winning Driver, Constructor, Report\nQ: What was the fastest lap at the Mexican Grand Prix?\nA: SELECT Fastest Lap FROM 1-1137718-2 WHERE Grand Prix = 'Mexican Grand Prix'"}
{"text": "table: 1-1137718-2\ncolumns: Rd., Grand Prix, Date, Location, Pole Position, Fastest Lap, Winning Driver, Constructor, Report\nQ: Which rd. took place at Hockenheimring?\nA: SELECT MIN Rd. FROM 1-1137718-2 WHERE Location = 'Hockenheimring'"}
{"text": "table: 1-1137718-2\ncolumns: Rd., Grand Prix, Date, Location, Pole Position, Fastest Lap, Winning Driver, Constructor, Report\nQ: How many drivers had the fastest lap at Silverstone?\nA: SELECT COUNT Fastest Lap FROM 1-1137718-2 WHERE Location = 'Silverstone'"}
{"text": "table: 1-11381701-3\ncolumns: Source, Date, Method, iOS, Android, BlackBerry, Symbian / Series 40, Bada, Windows, Other\nQ: What is the percentage of Android use when Windows is 1.15%?\nA: SELECT Android FROM 1-11381701-3 WHERE Windows = '1.15%'"}
{"text": "table: 1-11381701-3\ncolumns: Source, Date, Method, iOS, Android, BlackBerry, Symbian / Series 40, Bada, Windows, Other\nQ: On which dates was the value of Bada 0.05%?\nA: SELECT Date FROM 1-11381701-3 WHERE Bada = '0.05%'"}
{"text": "table: 1-11381701-3\ncolumns: Source, Date, Method, iOS, Android, BlackBerry, Symbian / Series 40, Bada, Windows, Other\nQ: When the value of \"other\" is 0.7%, what is the percentage for Windows?\nA: SELECT Windows FROM 1-11381701-3 WHERE Other = '0.7%'"}
{"text": "table: 1-11381701-3\ncolumns: Source, Date, Method, iOS, Android, BlackBerry, Symbian / Series 40, Bada, Windows, Other\nQ: When Symbian/Series 40 is 0.40%, what is the percentage of \"other\"?\nA: SELECT Other FROM 1-11381701-3 WHERE Symbian / Series 40 = '0.40%'"}
{"text": "table: 1-11381701-3\ncolumns: Source, Date, Method, iOS, Android, BlackBerry, Symbian / Series 40, Bada, Windows, Other\nQ: Which source shows Blackberry at 2.9%?\nA: SELECT Source FROM 1-11381701-3 WHERE BlackBerry = '2.9%'"}
{"text": "table: 1-11390711-4\ncolumns: English Name, Japanese orthography, Pronouciation, abbreviation, Provider(IAI), Foundation\nQ: Which colleges have the english abbreviation MTC?\nA: SELECT English Name FROM 1-11390711-4 WHERE abbreviation = 'MTC'"}
{"text": "table: 1-11390711-4\ncolumns: English Name, Japanese orthography, Pronouciation, abbreviation, Provider(IAI), Foundation\nQ: What is the Japanese orthography for the English name National Farmers Academy?\nA: SELECT Japanese orthography FROM 1-11390711-4 WHERE English Name = 'National Farmers Academy'"}
{"text": "table: 1-11390711-4\ncolumns: English Name, Japanese orthography, Pronouciation, abbreviation, Provider(IAI), Foundation\nQ: What is the abbreviation for the college pronounced \"k\u014dk\u016b daigakk\u014d\"?\nA: SELECT abbreviation FROM 1-11390711-4 WHERE Pronouciation = 'K\u014dk\u016b Daigakk\u014d'"}
{"text": "table: 1-11390711-4\ncolumns: English Name, Japanese orthography, Pronouciation, abbreviation, Provider(IAI), Foundation\nQ: How many providers were founded in 1964?\nA: SELECT COUNT Provider(IAI) FROM 1-11390711-4 WHERE Foundation = 1964"}
{"text": "table: 1-11390711-4\ncolumns: English Name, Japanese orthography, Pronouciation, abbreviation, Provider(IAI), Foundation\nQ: What is the Japanese orthography for National Fisheries University?\nA: SELECT Japanese orthography FROM 1-11390711-4 WHERE English Name = 'National Fisheries University'"}
{"text": "table: 1-11391954-3\ncolumns: Country, Total, Marathon (mens), Marathon (womens), Half Marathon (mens), Half Marathon (womens)\nQ: What is the minimum number for the half marathon (womens)?\nA: SELECT MIN Half Marathon (womens) FROM 1-11391954-3"}
{"text": "table: 1-11391954-3\ncolumns: Country, Total, Marathon (mens), Marathon (womens), Half Marathon (mens), Half Marathon (womens)\nQ: Whatis the total number of half marathon (mens) that represented kazakhstan?\nA: SELECT COUNT Half Marathon (mens) FROM 1-11391954-3 WHERE Country = 'Kazakhstan'"}
{"text": "table: 1-11391954-3\ncolumns: Country, Total, Marathon (mens), Marathon (womens), Half Marathon (mens), Half Marathon (womens)\nQ: What is amount of countries where half marathon (women) is larger than 1.0?\nA: SELECT COUNT Country FROM 1-11391954-3 WHERE Half Marathon (womens) > 1.0"}
{"text": "table: 1-11391954-3\ncolumns: Country, Total, Marathon (mens), Marathon (womens), Half Marathon (mens), Half Marathon (womens)\nQ: How many times is Moldova the winner of half marathon (womens)?\nA: SELECT COUNT Half Marathon (womens) FROM 1-11391954-3 WHERE Country = 'Moldova'"}
{"text": "table: 1-11391954-3\ncolumns: Country, Total, Marathon (mens), Marathon (womens), Half Marathon (mens), Half Marathon (womens)\nQ: Which country has half marathon (womens) that is larger than 1.0?\nA: SELECT Country FROM 1-11391954-3 WHERE Half Marathon (womens) > 1.0"}
{"text": "table: 1-1139087-2\ncolumns: Rd., Grand Prix, Date, Location, Pole Position, Fastest Lap, Winning Driver, Constructor, Report\nQ: What is the make of the car that won the brazilian grand prix?\nA: SELECT Constructor FROM 1-1139087-2 WHERE Grand Prix = 'Brazilian Grand Prix'"}
{"text": "table: 1-1139087-2\ncolumns: Rd., Grand Prix, Date, Location, Pole Position, Fastest Lap, Winning Driver, Constructor, Report\nQ: Who drove the fastest lap for round 8?\nA: SELECT Fastest Lap FROM 1-1139087-2 WHERE Rd. = 8"}
{"text": "table: 1-1139087-2\ncolumns: Rd., Grand Prix, Date, Location, Pole Position, Fastest Lap, Winning Driver, Constructor, Report\nQ: What day was the grand prix in jerez?\nA: SELECT Date FROM 1-1139087-2 WHERE Location = 'Jerez'"}
{"text": "table: 1-1139087-2\ncolumns: Rd., Grand Prix, Date, Location, Pole Position, Fastest Lap, Winning Driver, Constructor, Report\nQ: What event was in detroit?\nA: SELECT Grand Prix FROM 1-1139087-2 WHERE Location = 'Detroit'"}
{"text": "table: 1-1139087-2\ncolumns: Rd., Grand Prix, Date, Location, Pole Position, Fastest Lap, Winning Driver, Constructor, Report\nQ: How many events did nigel mansell drive the fastest and a mclaren - honda win?\nA: SELECT COUNT Grand Prix FROM 1-1139087-2 WHERE Constructor = 'McLaren - Honda' AND Fastest Lap = 'Nigel Mansell'"}
{"text": "table: 1-1139087-2\ncolumns: Rd., Grand Prix, Date, Location, Pole Position, Fastest Lap, Winning Driver, Constructor, Report\nQ: What day is the french grand prix\nA: SELECT Date FROM 1-1139087-2 WHERE Grand Prix = 'French Grand Prix'"}
{"text": "table: 1-1139835-3\ncolumns: Year, Winners, Grand Finalist, Scores, Venue, Crowd, Margin, Season Result\nQ:  who is the\u00a0winners\u00a0where\u00a0season result\u00a0is 7th\nA: SELECT Winners FROM 1-1139835-3 WHERE Season Result = '7th'"}
{"text": "table: 1-1139835-3\ncolumns: Year, Winners, Grand Finalist, Scores, Venue, Crowd, Margin, Season Result\nQ:  who is the\u00a0winners\u00a0where\u00a0season result\u00a0is 9th\nA: SELECT Winners FROM 1-1139835-3 WHERE Season Result = '9th'"}
{"text": "table: 1-1139835-3\ncolumns: Year, Winners, Grand Finalist, Scores, Venue, Crowd, Margin, Season Result\nQ:  what's the\u00a0grand finalist\u00a0where\u00a0winners\u00a0is collingwood\nA: SELECT Grand Finalist FROM 1-1139835-3 WHERE Winners = 'Collingwood'"}
{"text": "table: 1-1139835-3\ncolumns: Year, Winners, Grand Finalist, Scores, Venue, Crowd, Margin, Season Result\nQ:  who is the\u00a0season result\u00a0where\u00a0margin\u00a0is 51\nA: SELECT Season Result FROM 1-1139835-3 WHERE Margin = 51"}
{"text": "table: 1-1139835-3\ncolumns: Year, Winners, Grand Finalist, Scores, Venue, Crowd, Margin, Season Result\nQ:  who is the\u00a0grand finalist\u00a0where\u00a0scores\u00a0is 11.11 (77) \u2013 10.8 (68)\nA: SELECT Grand Finalist FROM 1-1139835-3 WHERE Scores = '11.11 (77) \u2013 10.8 (68)'"}
{"text": "table: 1-1139835-3\ncolumns: Year, Winners, Grand Finalist, Scores, Venue, Crowd, Margin, Season Result\nQ:  who is the\u00a0grand finalist\u00a0where\u00a0scores\u00a0is 8.9 (57) \u2013 7.12 (54)\nA: SELECT Grand Finalist FROM 1-1139835-3 WHERE Scores = '8.9 (57) \u2013 7.12 (54)'"}
{"text": "table: 1-1139835-1\ncolumns: Year, Winners, Grand Finalist, Scores, Venue, Crowd, Margin, Season Result\nQ: what was the crowd when the scores are 10.12 (72) \u2013 8.11 (59)?\nA: SELECT MAX Crowd FROM 1-1139835-1 WHERE Scores = '10.12 (72) \u2013 8.11 (59)'"}
{"text": "table: 1-1139835-1\ncolumns: Year, Winners, Grand Finalist, Scores, Venue, Crowd, Margin, Season Result\nQ: what is the venue where the scores are 15.13 (103) \u2013 8.4 (52)?\nA: SELECT Venue FROM 1-1139835-1 WHERE Scores = '15.13 (103) \u2013 8.4 (52)'"}
{"text": "table: 1-1139835-1\ncolumns: Year, Winners, Grand Finalist, Scores, Venue, Crowd, Margin, Season Result\nQ: what is the venue where the margin is 4?\nA: SELECT Venue FROM 1-1139835-1 WHERE Margin = 4"}
{"text": "table: 1-1139835-1\ncolumns: Year, Winners, Grand Finalist, Scores, Venue, Crowd, Margin, Season Result\nQ: what is the crowd when the grand finalist was south melbourne?\nA: SELECT Crowd FROM 1-1139835-1 WHERE Grand Finalist = 'South Melbourne'"}
{"text": "table: 1-1140067-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: What was the date for monaco grand prix?\nA: SELECT Date FROM 1-1140067-2 WHERE Race = 'Monaco Grand Prix'"}
{"text": "table: 1-1140067-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: What was the date for the pole position of alain prost?\nA: SELECT Date FROM 1-1140067-2 WHERE Pole Position = 'Alain Prost'"}
{"text": "table: 1-1140067-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: What is the race winer of the portuguese grand prix?\nA: SELECT Race Winner FROM 1-1140067-2 WHERE Race = 'Portuguese Grand Prix'"}
{"text": "table: 1-1140074-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: what's the\u00a0race winner\u00a0with\u00a0date\u00a0being 12 june\nA: SELECT Race Winner FROM 1-1140074-2 WHERE Date = '12 June'"}
{"text": "table: 1-1140074-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: what's the\u00a0constructor\u00a0with\u00a0location\u00a0being hockenheimring\nA: SELECT Constructor FROM 1-1140074-2 WHERE Location = 'Hockenheimring'"}
{"text": "table: 1-1140074-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: what's the\u00a0race winner\u00a0with\u00a0location\u00a0being jacarepagu\u00e1\nA: SELECT Race Winner FROM 1-1140074-2 WHERE Location = 'Jacarepagu\u00e1'"}
{"text": "table: 1-1140074-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: what's the total number of\u00a0race winner\u00a0with\u00a0rnd\u00a0being 10\nA: SELECT COUNT Race Winner FROM 1-1140074-2 WHERE Rnd = 10"}
{"text": "table: 1-1140074-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: what's the\u00a0pole position\u00a0with\u00a0location\u00a0being hockenheimring\nA: SELECT Pole Position FROM 1-1140074-2 WHERE Location = 'Hockenheimring'"}
{"text": "table: 1-1140074-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: what's the\u00a0report\u00a0with\u00a0rnd\u00a0being 4\nA: SELECT Report FROM 1-1140074-2 WHERE Rnd = 4"}
{"text": "table: 1-1139835-9\ncolumns: Season, Premier, Runner Up, Score, Venue, Attendance, Premiership\nQ: What venue has an attendance of 30824 at Essendon in 1984?\nA: SELECT Venue FROM 1-1139835-9 WHERE Premier = 'Essendon' AND Attendance = 30824"}
{"text": "table: 1-1139835-9\ncolumns: Season, Premier, Runner Up, Score, Venue, Attendance, Premiership\nQ: What other venue was a runner up to Hawthorn?\nA: SELECT Venue FROM 1-1139835-9 WHERE Runner Up = 'Hawthorn'"}
{"text": "table: 1-1139835-9\ncolumns: Season, Premier, Runner Up, Score, Venue, Attendance, Premiership\nQ: What is the other premiership when the runner up wis Geelong?\nA: SELECT Premiership FROM 1-1139835-9 WHERE Runner Up = 'Geelong'"}
{"text": "table: 1-1139835-9\ncolumns: Season, Premier, Runner Up, Score, Venue, Attendance, Premiership\nQ: Who are all the runner ups when the score is 9.12 (66) \u2013 5.6 (36)?\nA: SELECT Runner Up FROM 1-1139835-9 WHERE Score = '9.12 (66) \u2013 5.6 (36)'"}
{"text": "table: 1-1140073-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: Who had the fastest lap in the race where Patrick Tambay was on the pole?\nA: SELECT Fastest Lap FROM 1-1140073-2 WHERE Pole Position = 'Patrick Tambay'"}
{"text": "table: 1-1140073-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: What race had Nelson Piquet on the pole and was in N\u00fcrburgring?\nA: SELECT Race FROM 1-1140073-2 WHERE Pole Position = 'Nelson Piquet' AND Location = 'N\u00fcrburgring'"}
{"text": "table: 1-1140073-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: How many rounds did Patrick Tambay record the fastest lap?\nA: SELECT COUNT Rnd FROM 1-1140073-2 WHERE Fastest Lap = 'Patrick Tambay'"}
{"text": "table: 1-1140073-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: Which race is located in kyalami?\nA: SELECT Race FROM 1-1140073-2 WHERE Location = 'Kyalami'"}
{"text": "table: 1-1140077-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: What is the fastest lap with pole position of gilles villeneuve?\nA: SELECT Fastest Lap FROM 1-1140077-2 WHERE Pole Position = 'Gilles Villeneuve'"}
{"text": "table: 1-1140077-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: Who did the fastest lap in the dutch grand prix?\nA: SELECT Fastest Lap FROM 1-1140077-2 WHERE Race = 'Dutch Grand Prix'"}
{"text": "table: 1-1140077-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: Who did the fastest lap with the race winner john watson?\nA: SELECT Fastest Lap FROM 1-1140077-2 WHERE Race Winner = 'John Watson'"}
{"text": "table: 1-1140076-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: What is the constructor for 9 May?\nA: SELECT Constructor FROM 1-1140076-2 WHERE Date = '9 May'"}
{"text": "table: 1-1140076-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: What is the pole position for the race with the fastest lap by Nelson Piquet and the constructor is Ferrari?\nA: SELECT Pole Position FROM 1-1140076-2 WHERE Fastest Lap = 'Nelson Piquet' AND Constructor = 'Ferrari'"}
{"text": "table: 1-1140076-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: What is the report listed for the race in San Marino Grand Prix?\nA: SELECT Report FROM 1-1140076-2 WHERE Race = 'San Marino Grand Prix'"}
{"text": "table: 1-1140076-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: Who was the constructor in the location Monza?\nA: SELECT Constructor FROM 1-1140076-2 WHERE Location = 'Monza'"}
{"text": "table: 1-1140076-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: How many races had the pole position Alain Prost and the race winner Keke Rosberg?\nA: SELECT COUNT Race FROM 1-1140076-2 WHERE Pole Position = 'Alain Prost' AND Race Winner = 'Keke Rosberg'"}
{"text": "table: 1-1140080-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: what's the\u00a0report\u00a0with\u00a0location\u00a0 \u00f6sterreichring\nA: SELECT Report FROM 1-1140080-2 WHERE Location = '\u00d6sterreichring'"}
{"text": "table: 1-1140080-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: what's the\u00a0report\u00a0with\u00a0race\u00a0argentine grand prix\nA: SELECT Report FROM 1-1140080-2 WHERE Race = 'Argentine Grand Prix'"}
{"text": "table: 1-1140080-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: what's the minimum\u00a0rnd\u00a0with\u00a0race\u00a0 italian grand prix\nA: SELECT MIN Rnd FROM 1-1140080-2 WHERE Race = 'Italian Grand Prix'"}
{"text": "table: 1-1140080-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: what's the total number of\u00a0report\u00a0with\u00a0date\u00a0 29 april\nA: SELECT COUNT Report FROM 1-1140080-2 WHERE Date = '29 April'"}
{"text": "table: 1-1140080-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: what's the\u00a0race winner\u00a0with\u00a0constructor\u00a0 renault\nA: SELECT Race Winner FROM 1-1140080-2 WHERE Constructor = 'Renault'"}
{"text": "table: 1-1140080-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: what's the\u00a0date\u00a0with\u00a0rnd\u00a0 1\nA: SELECT Date FROM 1-1140080-2 WHERE Rnd = 1"}
{"text": "table: 1-1140083-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: How many days is the Monaco Grand Prix?\nA: SELECT COUNT Date FROM 1-1140083-2 WHERE Race = 'Monaco Grand Prix'"}
{"text": "table: 1-1140083-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: How many rounds were won with James Hunt as pole position and John Watson as  fastest lap?\nA: SELECT COUNT Rnd FROM 1-1140083-2 WHERE Pole Position = 'James Hunt' AND Fastest Lap = 'John Watson'"}
{"text": "table: 1-1140083-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: The Dijon-prenois had how many fastest laps?\nA: SELECT COUNT Fastest Lap FROM 1-1140083-2 WHERE Location = 'Dijon-Prenois'"}
{"text": "table: 1-1140083-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: What was the constructor for round 15?\nA: SELECT Constructor FROM 1-1140083-2 WHERE Rnd = 15"}
{"text": "table: 1-1140088-6\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: Who won the Brands Hatch circuit?\nA: SELECT Winning driver FROM 1-1140088-6 WHERE Circuit = 'Brands Hatch'"}
{"text": "table: 1-1140088-6\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: Who constructed the I Italian Republic Grand Prix?\nA: SELECT Constructor FROM 1-1140088-6 WHERE Race Name = 'I Italian Republic Grand Prix'"}
{"text": "table: 1-1140088-6\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: What race was held at Oulton Park?\nA: SELECT Race Name FROM 1-1140088-6 WHERE Circuit = 'Oulton Park'"}
{"text": "table: 1-1140088-6\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: Did the I Brazilian Grand Prix have a report?\nA: SELECT Report FROM 1-1140088-6 WHERE Race Name = 'I Brazilian Grand Prix'"}
{"text": "table: 1-1140085-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: what is the race where the pole position is niki lauda and the date is 27 april?\nA: SELECT Race FROM 1-1140085-2 WHERE Pole Position = 'Niki Lauda' AND Date = '27 April'"}
{"text": "table: 1-1140085-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: what is the date where the constructor is ferrari and the location is anderstorp?\nA: SELECT Date FROM 1-1140085-2 WHERE Constructor = 'Ferrari' AND Location = 'Anderstorp'"}
{"text": "table: 1-1140085-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: how many times is the pole position niki lauda and the race is monaco grand prix?\nA: SELECT COUNT Rnd FROM 1-1140085-2 WHERE Pole Position = 'Niki Lauda' AND Race = 'Monaco Grand Prix'"}
{"text": "table: 1-1140085-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: what is the report where the location is kyalami?\nA: SELECT Report FROM 1-1140085-2 WHERE Location = 'Kyalami'"}
{"text": "table: 1-1140085-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: who is the pole position for the rnd 3\nA: SELECT Pole Position FROM 1-1140085-2 WHERE Rnd = 3"}
{"text": "table: 1-1140085-2\ncolumns: Rnd, Race, Date, Location, Pole Position, Fastest Lap, Race Winner, Constructor, Report\nQ: what is the race where the fastest lap is by jean-pierre jarier?\nA: SELECT Race FROM 1-1140085-2 WHERE Fastest Lap = 'Jean-Pierre Jarier'"}
{"text": "table: 1-1140090-6\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: What circuit did Clay Regazzoni win?\nA: SELECT Circuit FROM 1-1140090-6 WHERE Winning driver = 'Clay Regazzoni'"}
{"text": "table: 1-1140090-6\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: What was the date when Chris Amon won?\nA: SELECT Date FROM 1-1140090-6 WHERE Winning driver = 'Chris Amon'"}
{"text": "table: 1-1140090-6\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: What circuit is the Vi Rhein-Pokalrennen race in?\nA: SELECT Circuit FROM 1-1140090-6 WHERE Race Name = 'VI Rhein-Pokalrennen'"}
{"text": "table: 1-1140103-6\ncolumns: #, Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: What date is listed at place 13\nA: SELECT Date FROM 1-1140103-6 WHERE # = 13"}
{"text": "table: 1-1140103-6\ncolumns: #, Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: What date has a solitudering circuit\nA: SELECT Date FROM 1-1140103-6 WHERE Circuit = 'Solitudering'"}
{"text": "table: 1-1140103-6\ncolumns: #, Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: How many dates have silverstone circuit\nA: SELECT COUNT Date FROM 1-1140103-6 WHERE Circuit = 'Silverstone'"}
{"text": "table: 1-1140103-6\ncolumns: #, Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: How many constructors are listed for the XVI BRDC international trophy race\nA: SELECT COUNT Constructor FROM 1-1140103-6 WHERE Race Name = 'XVI BRDC International Trophy'"}
{"text": "table: 1-1140105-6\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: What is the name of the circuit in which the race name is ii danish grand prix?\nA: SELECT Circuit FROM 1-1140105-6 WHERE Race Name = 'II Danish Grand Prix'"}
{"text": "table: 1-1140105-6\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: What is te name of the constructors dated 26 march?\nA: SELECT Constructor FROM 1-1140105-6 WHERE Date = '26 March'"}
{"text": "table: 1-1140105-6\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: What is the total amount of circuts dated 22 april?\nA: SELECT COUNT Circuit FROM 1-1140105-6 WHERE Date = '22 April'"}
{"text": "table: 1-1140105-6\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: what is the name of the constructor that has the circuit zeltweg airfield?\nA: SELECT Constructor FROM 1-1140105-6 WHERE Circuit = 'Zeltweg Airfield'"}
{"text": "table: 1-1140105-6\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: What is the name of the winning driver where the circuit name is posillipo?\nA: SELECT Winning driver FROM 1-1140105-6 WHERE Circuit = 'Posillipo'"}
{"text": "table: 1-1140105-6\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: What is the name of the circuit where the race xi Syracuse grand prix was held?\nA: SELECT Circuit FROM 1-1140105-6 WHERE Race Name = 'XI Syracuse Grand Prix'"}
{"text": "table: 1-1140111-5\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: What kind of report is for the Pau circuit?\nA: SELECT Report FROM 1-1140111-5 WHERE Circuit = 'Pau'"}
{"text": "table: 1-1140111-5\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: How many different kinds of reports are there for races that Juan Manuel Fangio won?\nA: SELECT COUNT Report FROM 1-1140111-5 WHERE Winning driver = 'Juan Manuel Fangio'"}
{"text": "table: 1-1140111-5\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: Who constructed the Syracuse circuit?\nA: SELECT Constructor FROM 1-1140111-5 WHERE Circuit = 'Syracuse'"}
{"text": "table: 1-1140116-5\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: What is the name of the race in the Modena circuit?\nA: SELECT Race Name FROM 1-1140116-5 WHERE Circuit = 'Modena'"}
{"text": "table: 1-1140116-5\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: What is the race name in the Monza circuit?\nA: SELECT Race Name FROM 1-1140116-5 WHERE Circuit = 'Monza'"}
{"text": "table: 1-1140116-5\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: When does V Madgwick Cup take place?\nA: SELECT Date FROM 1-1140116-5 WHERE Race Name = 'V Madgwick Cup'"}
{"text": "table: 1-1140116-5\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: Which driver won the race xiv el\u00e4intarhanajot?\nA: SELECT Winning driver FROM 1-1140116-5 WHERE Race Name = 'XIV El\u00e4intarhanajot'"}
{"text": "table: 1-1140116-5\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: Who won the Modena circuit?\nA: SELECT Winning driver FROM 1-1140116-5 WHERE Circuit = 'Modena'"}
{"text": "table: 1-1140113-5\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: How many constructors won the III Redex Trophy?\nA: SELECT COUNT Constructor FROM 1-1140113-5 WHERE Race Name = 'III RedeX Trophy'"}
{"text": "table: 1-1140113-5\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: What was the report of Mike Hawthorn's winning race?\nA: SELECT Report FROM 1-1140113-5 WHERE Winning driver = 'Mike Hawthorn'"}
{"text": "table: 1-1140113-5\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: What was the name of the race in Bordeaux?\nA: SELECT Race Name FROM 1-1140113-5 WHERE Circuit = 'Bordeaux'"}
{"text": "table: 1-1140117-5\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: What is the report for the race name V Ulster Trophy?\nA: SELECT Report FROM 1-1140117-5 WHERE Race Name = 'V Ulster Trophy'"}
{"text": "table: 1-1140117-5\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: What is the constructor for the Silverstone circuit?\nA: SELECT Constructor FROM 1-1140117-5 WHERE Circuit = 'Silverstone'"}
{"text": "table: 1-1140117-5\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: What's the report for the Silverstone circuit?\nA: SELECT Report FROM 1-1140117-5 WHERE Circuit = 'Silverstone'"}
{"text": "table: 1-1140117-5\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: What's the report for the race name, XIII Grand Prix de l'Albigeois?\nA: SELECT Report FROM 1-1140117-5 WHERE Race Name = 'XIII Grand Prix de l'Albigeois'"}
{"text": "table: 1-1140117-5\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: Which date did the race name XII Pau Grand Prix take place on?\nA: SELECT Date FROM 1-1140117-5 WHERE Race Name = 'XII Pau Grand Prix'"}
{"text": "table: 1-1140117-5\ncolumns: Race Name, Circuit, Date, Winning driver, Constructor, Report\nQ: Who was the winning driver for the goodwood circuit?\nA: SELECT Winning driver FROM 1-1140117-5 WHERE Circuit = 'Goodwood'"}
{"text": "table: 1-11411026-2\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: How many millions of U.S. viewers whatched episodes written by Krystal Houghton?\nA: SELECT U.S. viewers (millions) FROM 1-11411026-2 WHERE Written by = 'Krystal Houghton'"}
{"text": "table: 1-11411026-2\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: How many titles were directed in series 79?\nA: SELECT COUNT Directed by FROM 1-11411026-2 WHERE No. in series = 79"}
{"text": "table: 1-11411026-2\ncolumns: No. in series, No. in season, Title, Directed by, Written by, Original air date, U.S. viewers (millions)\nQ: Who wrote an episode watched by 19.01 million US viewers?\nA: SELECT Written by FROM 1-11411026-2 WHERE U.S. viewers (millions) = '19.01'"}
{"text": "table: 1-11404452-1\ncolumns: Series #, Episode title, Writer(s), Director, U.S. viewers (millions), Original air date\nQ: What are the titles of the episodes where Rodman Flender is the director?\nA: SELECT Episode title FROM 1-11404452-1 WHERE Director = 'Rodman Flender'"}
{"text": "table: 1-11404452-1\ncolumns: Series #, Episode title, Writer(s), Director, U.S. viewers (millions), Original air date\nQ: What is the original air date of the Jamie Babbit directed episode?\nA: SELECT Original air date FROM 1-11404452-1 WHERE Director = 'Jamie Babbit'"}
{"text": "table: 1-11404452-1\ncolumns: Series #, Episode title, Writer(s), Director, U.S. viewers (millions), Original air date\nQ: What is the original air date when there were 12.81 million u.s viewers?\nA: SELECT Original air date FROM 1-11404452-1 WHERE U.S. viewers (millions) = '12.81'"}
{"text": "table: 1-11404452-1\ncolumns: Series #, Episode title, Writer(s), Director, U.S. viewers (millions), Original air date\nQ: When did Shelia Lawrence join the series?\nA: SELECT MIN Series # FROM 1-11404452-1 WHERE Writer(s) = 'Shelia Lawrence'"}
{"text": "table: 1-11404452-1\ncolumns: Series #, Episode title, Writer(s), Director, U.S. viewers (millions), Original air date\nQ: Who was the director when there were 13.66 million u.s viewers?\nA: SELECT Director FROM 1-11404452-1 WHERE U.S. viewers (millions) = '13.66'"}
{"text": "table: 1-1143966-1\ncolumns: Season, Games, Won, Lost, Tied, Points, Pct %, Goals for, Goals against, Standing\nQ: Name the percentage where the amount won was 25\nA: SELECT Pct % FROM 1-1143966-1 WHERE Won = 25"}
{"text": "table: 1-1143966-1\ncolumns: Season, Games, Won, Lost, Tied, Points, Pct %, Goals for, Goals against, Standing\nQ: How many games were won with 2nd oha was standing and there were 62 games?\nA: SELECT Won FROM 1-1143966-1 WHERE Standing = '2nd OHA' AND Games = 62"}
{"text": "table: 1-11447995-2\ncolumns: Ward, Bello, Ben-Tahir, Doucet, Furtenbacher, Gauthier, Haydon, Larter, Lawrance, Libweshya, Liscumb\nQ: What is the Liscumb when Gauthier is 34?\nA: SELECT Liscumb FROM 1-11447995-2 WHERE Gauthier = '34'"}
{"text": "table: 1-11447995-2\ncolumns: Ward, Bello, Ben-Tahir, Doucet, Furtenbacher, Gauthier, Haydon, Larter, Lawrance, Libweshya, Liscumb\nQ: What is the Bello when Ben-Tahir is 296?\nA: SELECT Bello FROM 1-11447995-2 WHERE Ben-Tahir = '296'"}
{"text": "table: 1-11447995-2\ncolumns: Ward, Bello, Ben-Tahir, Doucet, Furtenbacher, Gauthier, Haydon, Larter, Lawrance, Libweshya, Liscumb\nQ: What is Ben-Tahir when Bello is 51?\nA: SELECT Ben-Tahir FROM 1-11447995-2 WHERE Bello = '51'"}
{"text": "table: 1-11447995-2\ncolumns: Ward, Bello, Ben-Tahir, Doucet, Furtenbacher, Gauthier, Haydon, Larter, Lawrance, Libweshya, Liscumb\nQ: What is Haydon when Larter is 11 and Libweshya is 4?\nA: SELECT Haydon FROM 1-11447995-2 WHERE Larter = '11' AND Libweshya = '4'"}
{"text": "table: 1-11447995-2\ncolumns: Ward, Bello, Ben-Tahir, Doucet, Furtenbacher, Gauthier, Haydon, Larter, Lawrance, Libweshya, Liscumb\nQ: What is Liscumb when Haydon is 1632?\nA: SELECT Liscumb FROM 1-11447995-2 WHERE Haydon = '1632'"}
{"text": "table: 1-11447995-2\ncolumns: Ward, Bello, Ben-Tahir, Doucet, Furtenbacher, Gauthier, Haydon, Larter, Lawrance, Libweshya, Liscumb\nQ: What is Doucet when Lawrance is 36?\nA: SELECT Doucet FROM 1-11447995-2 WHERE Lawrance = '36'"}
{"text": "table: 1-11449590-2\ncolumns: Week, Date, Opponent, Result, Kickoff [a ], Game site, TV, Attendance, Record\nQ: Which tv had the date december 7, 1986?\nA: SELECT TV FROM 1-11449590-2 WHERE Date = 'December 7, 1986'"}
{"text": "table: 1-11449590-2\ncolumns: Week, Date, Opponent, Result, Kickoff [a ], Game site, TV, Attendance, Record\nQ: Which kickoff has the opponent at new orleans saints?\nA: SELECT Kickoff [a ] FROM 1-11449590-2 WHERE Opponent = 'at New Orleans Saints'"}
{"text": "table: 1-11464746-1\ncolumns: House Name, Composition, Named after, Founded, Colours\nQ: How many houses are green?\nA: SELECT COUNT House Name FROM 1-11464746-1 WHERE Colours = 'Green'"}
{"text": "table: 1-11464746-1\ncolumns: House Name, Composition, Named after, Founded, Colours\nQ: What year was the house named gongola made?\nA: SELECT Founded FROM 1-11464746-1 WHERE House Name = 'Gongola'"}
{"text": "table: 1-11464746-1\ncolumns: House Name, Composition, Named after, Founded, Colours\nQ: What is the name of the green house?\nA: SELECT House Name FROM 1-11464746-1 WHERE Colours = 'Green'"}
{"text": "table: 1-11464746-1\ncolumns: House Name, Composition, Named after, Founded, Colours\nQ: What is the green house made of?\nA: SELECT Composition FROM 1-11464746-1 WHERE Colours = 'Green'"}
{"text": "table: 1-11464746-1\ncolumns: House Name, Composition, Named after, Founded, Colours\nQ: What is the benue house made of?\nA: SELECT Composition FROM 1-11464746-1 WHERE House Name = 'Benue'"}
{"text": "table: 1-11465521-2\ncolumns: Week, Date, Opponent, Result, Kickoff [a ], Game site, TV, Attendance, Record\nQ: In which week was the game against a team with a record of 3-6 played?\nA: SELECT COUNT Week FROM 1-11465521-2 WHERE Record = '3-6'"}
{"text": "table: 1-11465521-2\ncolumns: Week, Date, Opponent, Result, Kickoff [a ], Game site, TV, Attendance, Record\nQ: Which channel had the game against the Minnesota Vikings?\nA: SELECT TV FROM 1-11465521-2 WHERE Opponent = 'Minnesota Vikings'"}
{"text": "table: 1-11465521-2\ncolumns: Week, Date, Opponent, Result, Kickoff [a ], Game site, TV, Attendance, Record\nQ: How many opponents were there at the game with 64,087 people in attendance?\nA: SELECT COUNT Opponent FROM 1-11465521-2 WHERE Attendance = '64,087'"}
{"text": "table: 1-11452830-2\ncolumns: Week, Date, Opponent, Result, Kickoff [a ], Game site, TV, Attendance, Record\nQ: Where was the game played when the team's record was 1-3? \nA: SELECT Game site FROM 1-11452830-2 WHERE Record = '1-3'"}
{"text": "table: 1-11452830-2\ncolumns: Week, Date, Opponent, Result, Kickoff [a ], Game site, TV, Attendance, Record\nQ: How many times was there a kickoff in the September 4, 1988 game? \nA: SELECT COUNT Kickoff [a ] FROM 1-11452830-2 WHERE Date = 'September 4, 1988'"}
{"text": "table: 1-11452830-2\ncolumns: Week, Date, Opponent, Result, Kickoff [a ], Game site, TV, Attendance, Record\nQ: How many crowds watched the game where the record was 1-3? \nA: SELECT COUNT Attendance FROM 1-11452830-2 WHERE Record = '1-3'"}
{"text": "table: 1-1149495-1\ncolumns: Series, Year, Winner, Runner-up, Third place, Fourth place, Fifth place, Sixth place, Host\nQ: What year finished with Daniel Zueras as the runner-up?\nA: SELECT COUNT Year FROM 1-1149495-1 WHERE Runner-up = 'Daniel Zueras'"}
{"text": "table: 1-1149495-1\ncolumns: Series, Year, Winner, Runner-up, Third place, Fourth place, Fifth place, Sixth place, Host\nQ: How many people hosted the show in the year when Chenoa  ended up in fourth place?\nA: SELECT COUNT Host FROM 1-1149495-1 WHERE Fourth place = 'Chenoa'"}
{"text": "table: 1-1149495-1\ncolumns: Series, Year, Winner, Runner-up, Third place, Fourth place, Fifth place, Sixth place, Host\nQ: How many fourth places were there in 2003?\nA: SELECT COUNT Fourth place FROM 1-1149495-1 WHERE Year = '2003'"}
{"text": "table: 1-1147705-1\ncolumns: model, max. motive power, max. torque at rpm, engine displacement, engine type, engine configuration & notes 0-100km/h\nQ: What is the engine type when the max torque at rpm is n\u00b7m ( lbf\u00b7ft ) @ 4,800 Answers:?\nA: SELECT engine type FROM 1-1147705-1 WHERE max. torque at rpm = 'N\u00b7m ( lbf\u00b7ft ) @ 4,800'"}
{"text": "table: 1-1147705-1\ncolumns: model, max. motive power, max. torque at rpm, engine displacement, engine type, engine configuration & notes 0-100km/h\nQ: What is the engine configuration $notes 0-100km/h for the engine type b5244 t2?\nA: SELECT engine configuration & notes 0-100km/h FROM 1-1147705-1 WHERE engine type = 'B5244 T2'"}
{"text": "table: 1-1147705-1\ncolumns: model, max. motive power, max. torque at rpm, engine displacement, engine type, engine configuration & notes 0-100km/h\nQ: What is the engine displacement for the engine type b5254 t?\nA: SELECT engine displacement FROM 1-1147705-1 WHERE engine type = 'B5254 T'"}
{"text": "table: 1-1147705-1\ncolumns: model, max. motive power, max. torque at rpm, engine displacement, engine type, engine configuration & notes 0-100km/h\nQ: How many have are model 2.4 awd?\nA: SELECT COUNT engine type FROM 1-1147705-1 WHERE model = '2.4 AWD'"}
{"text": "table: 1-1147705-1\ncolumns: model, max. motive power, max. torque at rpm, engine displacement, engine type, engine configuration & notes 0-100km/h\nQ: How many engine b5204 t3?\nA: SELECT COUNT engine displacement FROM 1-1147705-1 WHERE engine type = 'B5204 T3'"}
{"text": "table: 1-1147705-1\ncolumns: model, max. motive power, max. torque at rpm, engine displacement, engine type, engine configuration & notes 0-100km/h\nQ: How many engine b5234 t3?\nA: SELECT COUNT model FROM 1-1147705-1 WHERE engine type = 'B5234 T3'"}
{"text": "table: 1-1147701-4\ncolumns: Model name, Power (ps), Torque (Nm@rpm), Displacement (cm\u00b3), Engine code, Comment\nQ:  how many\u00a0power (ps)\u00a0with\u00a0torque (nm@rpm)\u00a0being 240@2200-5000\nA: SELECT COUNT Power (ps) FROM 1-1147701-4 WHERE Torque (Nm@rpm) = '240@2200-5000'"}
{"text": "table: 1-1147701-4\ncolumns: Model name, Power (ps), Torque (Nm@rpm), Displacement (cm\u00b3), Engine code, Comment\nQ: what's the\u00a0comment\u00a0with\u00a0model name\u00a0being 2.4 (2001-2007)\nA: SELECT Comment FROM 1-1147701-4 WHERE Model name = '2.4 (2001-2007)'"}
{"text": "table: 1-1147701-4\ncolumns: Model name, Power (ps), Torque (Nm@rpm), Displacement (cm\u00b3), Engine code, Comment\nQ: what's the\u00a0model name\u00a0with\u00a0engine code\u00a0being b5204 t5\nA: SELECT Model name FROM 1-1147701-4 WHERE Engine code = 'B5204 T5'"}
{"text": "table: 1-1147701-4\ncolumns: Model name, Power (ps), Torque (Nm@rpm), Displacement (cm\u00b3), Engine code, Comment\nQ: what's the\u00a0dbeingplacement (cm\u00b3)\u00a0with\u00a0torque (nm@rpm)\u00a0being 350@1800-6000\nA: SELECT Displacement (cm\u00b3) FROM 1-1147701-4 WHERE Torque (Nm@rpm) = '350@1800-6000'"}
{"text": "table: 1-1147701-4\ncolumns: Model name, Power (ps), Torque (Nm@rpm), Displacement (cm\u00b3), Engine code, Comment\nQ: what's the\u00a0model name\u00a0with\u00a0torque (nm@rpm)\u00a0being 230@4500\nA: SELECT Model name FROM 1-1147701-4 WHERE Torque (Nm@rpm) = '230@4500'"}
{"text": "table: 1-1147701-4\ncolumns: Model name, Power (ps), Torque (Nm@rpm), Displacement (cm\u00b3), Engine code, Comment\nQ: what's the\u00a0model name\u00a0with\u00a0engine code\u00a0being b5254 t4\nA: SELECT Model name FROM 1-1147701-4 WHERE Engine code = 'B5254 T4'"}
{"text": "table: 1-1147701-5\ncolumns: Model name, Power (ps), Torque (Nm@rpm), Displacement (cm\u00b3), Engine code, Comment\nQ: Name the torque of the engine is d5244 t5\nA: SELECT Torque (Nm@rpm) FROM 1-1147701-5 WHERE Engine code = 'D5244 T5'"}
{"text": "table: 1-1147701-5\ncolumns: Model name, Power (ps), Torque (Nm@rpm), Displacement (cm\u00b3), Engine code, Comment\nQ: What is the model of the engine d5244 t?\nA: SELECT Model name FROM 1-1147701-5 WHERE Engine code = 'D5244 T'"}
{"text": "table: 1-1147701-5\ncolumns: Model name, Power (ps), Torque (Nm@rpm), Displacement (cm\u00b3), Engine code, Comment\nQ: What is the model of the enginge d5252 t?\nA: SELECT Model name FROM 1-1147701-5 WHERE Engine code = 'D5252 T'"}
{"text": "table: 1-1147701-5\ncolumns: Model name, Power (ps), Torque (Nm@rpm), Displacement (cm\u00b3), Engine code, Comment\nQ: What is the model of the engine d5244 t7?\nA: SELECT Model name FROM 1-1147701-5 WHERE Engine code = 'D5244 T7'"}
{"text": "table: 1-11545282-11\ncolumns: Player, No., Nationality, Position, Years for Jazz, School/Club Team\nQ: What is the position of number 47?\nA: SELECT Position FROM 1-11545282-11 WHERE No. = '47'"}
{"text": "table: 1-11545282-11\ncolumns: Player, No., Nationality, Position, Years for Jazz, School/Club Team\nQ: Name the position of Turkey\nA: SELECT Position FROM 1-11545282-11 WHERE Nationality = 'Turkey'"}
{"text": "table: 1-11545282-11\ncolumns: Player, No., Nationality, Position, Years for Jazz, School/Club Team\nQ: Who is player number 51?\nA: SELECT Player FROM 1-11545282-11 WHERE No. = '51'"}
{"text": "table: 1-11545282-11\ncolumns: Player, No., Nationality, Position, Years for Jazz, School/Club Team\nQ: What is the position for the years 1998-99\nA: SELECT Position FROM 1-11545282-11 WHERE Years for Jazz = '1998-99'"}
{"text": "table: 1-11545282-11\ncolumns: Player, No., Nationality, Position, Years for Jazz, School/Club Team\nQ: How many positions are for creighton?\nA: SELECT COUNT Position FROM 1-11545282-11 WHERE School/Club Team = 'Creighton'"}
{"text": "table: 1-11545282-12\ncolumns: Player, No., Nationality, Position, Years for Jazz, School/Club Team\nQ: Which player is from Marshall and played 1974-75?\nA: SELECT Player FROM 1-11545282-12 WHERE Years for Jazz = '1974-75' AND School/Club Team = 'Marshall'"}
{"text": "table: 1-11545282-12\ncolumns: Player, No., Nationality, Position, Years for Jazz, School/Club Team\nQ: Which country is the player that went to Oregon?\nA: SELECT Nationality FROM 1-11545282-12 WHERE School/Club Team = 'Oregon'"}
{"text": "table: 1-11545282-12\ncolumns: Player, No., Nationality, Position, Years for Jazz, School/Club Team\nQ: Which country is Jim Les from?\nA: SELECT Nationality FROM 1-11545282-12 WHERE Player = 'Jim Les'"}
{"text": "table: 1-11545282-12\ncolumns: Player, No., Nationality, Position, Years for Jazz, School/Club Team\nQ: Which number is the player from Minnesota?\nA: SELECT MAX No. FROM 1-11545282-12 WHERE School/Club Team = 'Minnesota'"}
{"text": "table: 1-11545282-18\ncolumns: Player, No., Nationality, Position, Years for Jazz, School/Club Team\nQ: Which player played for years 2000-02\nA: SELECT Player FROM 1-11545282-18 WHERE Years for Jazz = '2000-02'"}
{"text": "table: 1-11545282-18\ncolumns: Player, No., Nationality, Position, Years for Jazz, School/Club Team\nQ: Which school is Kirk Snyder from?\nA: SELECT School/Club Team FROM 1-11545282-18 WHERE Player = 'Kirk Snyder'"}
