# 🚀 Exo Distributed AI Setup - Other System Instructions

## ✅ Current Status on Windows System
- **Exo is running** with enhanced debugging (context7)
- **NVIDIA RTX 2080 Super detected** and working
- **Network services active** on `*************:52415`
- **Model downloading**: llama-3.2-3b (in progress)
- **API endpoint working**: Confirmed with test request

## Prerequisites for Remote System
1. **Python 3.12+** installed
2. **Git** installed
3. **Ollama** installed
4. Both systems connected to same router via ethernet

## Installation Steps

### 1. Install Exo
```bash
git clone https://github.com/exo-explore/exo.git
cd exo
pip install -e .
```

### 2. Install Required Models (IMPORTANT: Use correct names)
```bash
# Note: Use llama3.2:3b for Ollama, but llama-3.2-3b for Exo API
ollama pull llama3.2:3b
ollama pull mxbai-embed-large
```

### 3. Get Your IP Address
```bash
# On Windows:
ipconfig | findstr "IPv4"

# On Linux/Mac:
ip addr show | grep "inet 192.168"
# or
ifconfig | grep "inet 192.168"
```

### 4. Update Configuration
Edit the `exo_config.json` file on the Windows system:
- Replace `192.168.0.XXX` with your actual IP address
- Adjust memory/capabilities based on your system specs

### 5. Start Exo on Remote System

#### Option A: Auto Discovery (Recommended)
```bash
# Set debug environment variables
export DEBUG=7
export DEBUG_DISCOVERY=7
export TINYGRAD_DEBUG=2

# Start Exo with auto discovery
exo --inference-engine tinygrad \
    --node-id remote-node \
    --node-host 0.0.0.0 \
    --node-port 50052 \
    --listen-port 5678 \
    --broadcast-port 5679 \
    --chatgpt-api-port 52416 \
    --discovery-module udp \
    --discovery-timeout 30 \
    --default-model llama-3.2-3b \
    --max-generate-tokens 2048
```

#### Option B: Manual Discovery
```bash
# Set debug environment variables
export DEBUG=7
export DEBUG_DISCOVERY=7
export TINYGRAD_DEBUG=2

# Start Exo with manual discovery
exo --inference-engine tinygrad \
    --node-id remote-node \
    --node-host 0.0.0.0 \
    --node-port 50052 \
    --discovery-module manual \
    --discovery-config-path exo_config.json \
    --default-model llama-3.2-3b \
    --max-generate-tokens 2048
```

## Network Configuration

### Firewall Rules Needed
Allow these ports through firewall on both systems:
- **50051-50052**: Node communication
- **5678-5679**: Discovery and communication
- **52415-52416**: ChatGPT API endpoints

### Windows Firewall Commands
```cmd
netsh advfirewall firewall add rule name="Exo Node Ports" dir=in action=allow protocol=TCP localport=50051-50052
netsh advfirewall firewall add rule name="Exo Discovery" dir=in action=allow protocol=UDP localport=5678-5679
netsh advfirewall firewall add rule name="Exo API" dir=in action=allow protocol=TCP localport=52415-52416
```

### Linux Firewall Commands
```bash
sudo ufw allow 50051:50052/tcp
sudo ufw allow 5678:5679/udp
sudo ufw allow 52415:52416/tcp
```

## Verification

### Check Connection
1. Start Exo on Windows system first
2. Start Exo on remote system
3. Look for "Exo Cluster (2 nodes)" message
4. Both systems should show each other in the cluster

### Test API
```bash
curl -X POST http://*************:52415/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "llama3.2:3b",
    "messages": [{"role": "user", "content": "Hello from distributed AI!"}],
    "max_tokens": 100
  }'
```

## Troubleshooting

### Common Issues
1. **Firewall blocking**: Ensure all ports are open
2. **Different subnets**: Both devices must be on same network segment
3. **Model mismatch**: Ensure same models installed on both systems
4. **Port conflicts**: Check no other services using the ports

### Debug Mode
Add these environment variables for enhanced debugging:
```bash
export DEBUG=7
export DEBUG_DISCOVERY=7
export TINYGRAD_DEBUG=2
```
